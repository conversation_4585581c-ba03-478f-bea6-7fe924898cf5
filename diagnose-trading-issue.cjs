const MetaApi = require('metaapi.cloud-sdk').default;

async function diagnoseTradingIssue() {
  console.log('🔍 DIAGNOSING TRADING ISSUE...\n');

  const token = process.env.VITE_METAAPI_TOKEN;
  const accountId = process.env.VITE_MASTER_ACCOUNT_ID;

  console.log('📋 Environment Check:');
  console.log(`   Token: ${token ? 'Present ✅' : 'Missing ❌'}`);
  console.log(`   Account ID: ${accountId || 'Missing ❌'}`);

  if (!token || !accountId) {
    console.log('\n❌ CRITICAL: Environment variables missing');
    console.log('   The bot cannot work without proper MetaAPI credentials');
    return;
  }

  try {
    // Step 1: Test MetaAPI Connection
    console.log('\n🔍 Step 1: Testing MetaAPI Connection');
    const api = new MetaApi(token);
    const account = await api.metatraderAccountApi.getAccount(accountId);
    
    console.log(`   ✅ Account: ${account.name}`);
    console.log(`   ✅ State: ${account.state}`);
    console.log(`   ✅ Connection: ${account.connectionStatus}`);
    
    const isReady = account.state === 'DEPLOYED' && account.connectionStatus === 'CONNECTED';
    if (!isReady) {
      console.log('\n❌ CRITICAL: Account not ready for trading');
      console.log('   This is why the bot is not trading!');
      return;
    }
    
    // Step 2: Test Connection Instance
    console.log('\n🔍 Step 2: Testing Connection Instance');
    const connection = account.getStreamingConnection();
    await connection.connect();
    await connection.waitSynchronized();
    console.log('   ✅ Connected and synchronized');
    
    // Step 3: Test Market Data Access
    console.log('\n🔍 Step 3: Testing Market Data Access');
    const symbols = ['EURUSD', 'XAUUSD'];
    const marketData = {};
    
    for (const symbol of symbols) {
      try {
        const price = await connection.getSymbolPrice(symbol);
        const spread = price.ask - price.bid;
        marketData[symbol] = { price, spread };
        console.log(`   ✅ ${symbol}: ${price.bid}/${price.ask} (spread: ${spread.toFixed(5)})`);
      } catch (error) {
        console.log(`   ❌ ${symbol}: ${error.message}`);
        return;
      }
    }
    
    // Step 4: Test Account Information
    console.log('\n🔍 Step 4: Testing Account Information');
    try {
      const accountInfo = await connection.getAccountInformation();
      console.log(`   ✅ Balance: ${accountInfo.balance} ${accountInfo.currency}`);
      console.log(`   ✅ Equity: ${accountInfo.equity} ${accountInfo.currency}`);
      console.log(`   ✅ Free Margin: ${accountInfo.freeMargin} ${accountInfo.currency}`);
      
      if (accountInfo.freeMargin < 100) {
        console.log('   ⚠️  WARNING: Low free margin - may prevent trading');
      }
    } catch (error) {
      console.log(`   ❌ Account info error: ${error.message}`);
      return;
    }
    
    // Step 5: Test Current Positions
    console.log('\n🔍 Step 5: Testing Current Positions');
    try {
      const positions = await connection.getPositions();
      console.log(`   ✅ Open positions: ${positions.length}`);
      
      if (positions.length > 0) {
        console.log('   Current positions:');
        positions.forEach(pos => {
          console.log(`     - ${pos.symbol}: ${pos.type} ${pos.volume} @ ${pos.openPrice} (P&L: ${pos.unrealizedProfit})`);
        });
      }
    } catch (error) {
      console.log(`   ❌ Positions error: ${error.message}`);
      return;
    }
    
    // Step 6: Simulate Trading Conditions
    console.log('\n🔍 Step 6: Simulating Trading Conditions');
    
    for (const symbol of symbols) {
      const { price, spread } = marketData[symbol];
      
      console.log(`\n   📊 ${symbol} Trading Analysis:`);
      console.log(`      Current Price: ${price.bid}/${price.ask}`);
      console.log(`      Spread: ${spread.toFixed(5)} (${(spread * 10000).toFixed(1)} pips)`);
      
      // Check spread conditions
      const maxSpread = symbol === 'EURUSD' ? 0.0003 : 0.5; // 3 pips for EUR/USD, 50 cents for Gold
      const spreadOk = spread <= maxSpread;
      console.log(`      Spread Check: ${spreadOk ? '✅ GOOD' : '❌ TOO HIGH'} (max: ${maxSpread})`);
      
      // Simulate confidence calculation
      const baseConfidence = 60;
      const volatilityBonus = Math.random() * 20; // 0-20%
      const spreadPenalty = spreadOk ? 0 : -10;
      const randomFactor = Math.random() * 15; // 0-15%
      
      const simulatedConfidence = Math.round(baseConfidence + volatilityBonus + spreadPenalty + randomFactor);
      console.log(`      Simulated Confidence: ${simulatedConfidence}%`);
      
      // Check against thresholds
      const thresholds = { conservative: 80, balanced: 65, aggressive: 55 };
      
      for (const [level, threshold] of Object.entries(thresholds)) {
        const wouldTrade = simulatedConfidence >= threshold && spreadOk;
        console.log(`      ${level.toUpperCase()} (${threshold}%): ${wouldTrade ? '✅ WOULD TRADE' : '❌ NO TRADE'}`);
      }
    }
    
    // Step 7: Test Trade Execution Capability
    console.log('\n🔍 Step 7: Testing Trade Execution Capability');
    
    try {
      const symbolSpec = await connection.getSymbolSpecification('EURUSD');
      console.log(`   ✅ Symbol Specification Retrieved:`);
      console.log(`      Min Volume: ${symbolSpec.minVolume}`);
      console.log(`      Max Volume: ${symbolSpec.maxVolume}`);
      console.log(`      Volume Step: ${symbolSpec.volumeStep}`);
      console.log(`      Digits: ${symbolSpec.digits}`);
      
      // Check if we can calculate proper volume
      const minVolume = symbolSpec.minVolume;
      const volumeStep = symbolSpec.volumeStep;
      console.log(`   ✅ Can calculate trade volume: ${minVolume} (step: ${volumeStep})`);
      
    } catch (error) {
      console.log(`   ❌ Symbol specification error: ${error.message}`);
      return;
    }
    
    console.log('\n🎯 DIAGNOSIS SUMMARY:');
    console.log('   ✅ MetaAPI connection working');
    console.log('   ✅ Account ready for trading');
    console.log('   ✅ Market data accessible');
    console.log('   ✅ Account information available');
    console.log('   ✅ Trade execution capability confirmed');
    
    console.log('\n💡 POSSIBLE REASONS BOT IS NOT TRADING:');
    console.log('   1. Bot not actually running in browser (check console logs)');
    console.log('   2. Confidence levels too low (market conditions not favorable)');
    console.log('   3. Spread conditions not met (spreads too high)');
    console.log('   4. Daily trade limits reached');
    console.log('   5. Maximum open positions reached');
    console.log('   6. Browser environment issues (different from Node.js)');
    
    console.log('\n🔧 NEXT STEPS:');
    console.log('   1. Open browser developer console');
    console.log('   2. Look for "Trading Cycle" logs every 30 seconds');
    console.log('   3. Check for "Signal - Confidence" messages');
    console.log('   4. Verify bot status shows "Running: true"');
    console.log('   5. Use BotDebugger component to force manual cycle');
    
    await connection.close();
    
  } catch (error) {
    console.error('\n❌ DIAGNOSIS FAILED:', error.message);
    
    if (error.message.includes('not found')) {
      console.log('\n💡 Account ID issue detected');
      console.log('   Run: node -r dotenv/config list-accounts.cjs');
    } else if (error.message.includes('not deployed')) {
      console.log('\n💡 Account deployment issue');
      console.log('   Check MetaAPI dashboard and deploy account');
    } else if (error.message.includes('not connected')) {
      console.log('\n💡 Connection issue');
      console.log('   Check broker connection in MetaAPI dashboard');
    }
  }
}

diagnoseTradingIssue().catch(console.error);
