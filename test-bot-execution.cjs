const MetaApi = require('metaapi.cloud-sdk').default;

async function testBotExecution() {
  console.log('🤖 Testing Bot Trade Execution...\n');

  const token = process.env.VITE_METAAPI_TOKEN;
  const accountId = process.env.VITE_MASTER_ACCOUNT_ID;

  if (!token || !accountId) {
    console.log('❌ Missing environment variables');
    return;
  }

  try {
    const api = new MetaApi(token);
    const account = await api.metatraderAccountApi.getAccount(accountId);
    
    console.log('📊 Account Status:');
    console.log(`   Ready: ${account.state === 'DEPLOYED' && account.connectionStatus === 'CONNECTED'}`);
    console.log(`   Balance: Checking...`);
    
    // Get connection and account info
    const connection = account.getStreamingConnection();
    await connection.connect();
    await connection.waitSynchronized();
    
    try {
      const accountInfo = await connection.getAccountInformation();
      console.log(`   Balance: ${accountInfo.balance} ${accountInfo.currency}`);
      console.log(`   Equity: ${accountInfo.equity} ${accountInfo.currency}`);
      console.log(`   Free Margin: ${accountInfo.freeMargin} ${accountInfo.currency}`);
    } catch (error) {
      console.log(`   Balance: ❌ ${error.message}`);
    }
    
    // Test getting market data
    console.log('\n📈 Testing Market Data:');
    const symbols = ['EURUSD', 'XAUUSD', 'USDJPY'];
    
    for (const symbol of symbols) {
      try {
        const price = await connection.getSymbolPrice(symbol);
        console.log(`   ${symbol}: Bid ${price.bid} / Ask ${price.ask} (Spread: ${(price.ask - price.bid).toFixed(5)})`);
      } catch (error) {
        console.log(`   ${symbol}: ❌ ${error.message}`);
      }
    }
    
    // Test positions
    console.log('\n📋 Current Positions:');
    const positions = await connection.getPositions();
    if (positions.length === 0) {
      console.log('   No open positions');
    } else {
      positions.forEach(pos => {
        console.log(`   ${pos.symbol}: ${pos.type} ${pos.volume} lots @ ${pos.openPrice}`);
      });
    }
    
    // Test orders
    console.log('\n📝 Pending Orders:');
    const orders = await connection.getOrders();
    if (orders.length === 0) {
      console.log('   No pending orders');
    } else {
      orders.forEach(order => {
        console.log(`   ${order.symbol}: ${order.type} ${order.volume} lots @ ${order.openPrice}`);
      });
    }
    
    console.log('\n✅ Bot execution test completed successfully!');
    console.log('🎯 The bot should now be able to execute trades.');
    console.log('\n💡 Next steps:');
    console.log('   1. Check the browser console for bot activity');
    console.log('   2. Go to the Bot tab and use the BotDebugger');
    console.log('   3. Click "Force Cycle" to trigger manual trading');
    
    await connection.close();
    
  } catch (error) {
    console.error('❌ Bot execution test failed:', error.message);
  }
}

testBotExecution().catch(console.error);
