import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Activity } from 'lucide-react';
import { getMetaApiService, MarketData } from '../services/MetaApiService';

interface MarketDataWidgetProps {
  symbols?: string[];
}

const MarketDataWidget: React.FC<MarketDataWidgetProps> = ({ 
  symbols = ['EURUSD', 'XAUUSD', 'GBPUSD', 'USDJPY'] 
}) => {
  const [marketData, setMarketData] = useState<{ [symbol: string]: MarketData }>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  useEffect(() => {
    let isMounted = true;
    let retryCount = 0;
    const maxRetries = 3;

    const fetchMarketData = async () => {
      if (!isMounted) return;

      try {
        setError(null);

        const metaApiService = getMetaApiService();

        // Check service readiness first
        if (!metaApiService.isServiceReady()) {
          const initError = metaApiService.getInitializationError();
          throw new Error(initError || 'MetaAPI service not ready');
        }

        // Only connect if not healthy
        if (!metaApiService.isConnectionHealthy()) {
          const accountId = import.meta.env.VITE_MASTER_ACCOUNT_ID || '168683';
          await metaApiService.connect(accountId);
        }

        const data = await metaApiService.getMultipleMarketData(symbols);

        if (isMounted) {
          setMarketData(data);
          setLoading(false);
          retryCount = 0; // Reset retry count on success
        }
      } catch (err) {
        console.error('Failed to fetch market data:', err);

        if (isMounted) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to fetch market data';

          // Implement retry logic for transient errors
          if (retryCount < maxRetries && (
            errorMessage.includes('rate limit') ||
            errorMessage.includes('timeout') ||
            errorMessage.includes('network')
          )) {
            retryCount++;
            console.log(`Retrying market data fetch (${retryCount}/${maxRetries})...`);
            setTimeout(() => fetchMarketData(), 2000 * retryCount); // Exponential backoff
            return;
          }

          setError(errorMessage);
          setLoading(false);
        }
      }
    };

    fetchMarketData();

    // Update market data every 3 seconds (optimized from 5 seconds)
    const interval = setInterval(fetchMarketData, 3000);

    return () => {
      isMounted = false;
      clearInterval(interval);
      // Note: Don't disconnect singleton service as other components may be using it
    };
  }, [symbols]);

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-center space-x-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading market data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="text-center text-red-600">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      <div className="flex items-center space-x-2 mb-4">
        <Activity className="w-5 h-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Live Market Data</h3>
        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {symbols.map((symbol) => {
          const data = marketData[symbol];
          if (!data) {
            return (
              <div key={symbol} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="font-semibold text-gray-900 dark:text-white">{symbol}</span>
                  <span className="text-sm text-gray-500">Loading...</span>
                </div>
              </div>
            );
          }

          const spread = data.spread || (data.ask - data.bid);
          const midPrice = (data.bid + data.ask) / 2;

          return (
            <div key={symbol} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <div className="flex items-center justify-between mb-2">
                <span className="font-semibold text-gray-900 dark:text-white">{symbol}</span>
                <div className="flex items-center space-x-1">
                  <Activity className="w-3 h-3 text-green-500" />
                  <span className="text-xs text-green-600">LIVE</span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Bid</p>
                  <p className="font-mono font-semibold text-red-600">{data.bid.toFixed(5)}</p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Ask</p>
                  <p className="font-mono font-semibold text-green-600">{data.ask.toFixed(5)}</p>
                </div>
              </div>
              
              <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
                  <span>Spread: {(spread * (symbol.includes('JPY') ? 100 : 10000)).toFixed(1)} pips</span>
                  <span>{data.time.toLocaleTimeString()}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MarketDataWidget;