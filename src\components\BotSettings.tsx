import React, { useState, useEffect, useCallback } from 'react';
import { useTradingBot } from '../hooks/useTradingBot';
import { BotConfig } from '../ai/TradingBot';
import { getMetaApiService } from '../services/MetaApiService';
import { 
  Settings, 
  TrendingUp, 
  Shield, 
  Brain, 
  Bell, 
  Database,
  Save,
  Download,
  Upload,
  RotateCcw,
  CheckCircle,
  AlertTriangle,
  Info,
  Zap
} from 'lucide-react';

// Extended configuration interface
interface ExtendedBotConfig extends BotConfig {
  tradingHours: {
    start: string;
    end: string;
    timezone: string;
  };
  positionSizing: {
    method: 'fixed' | 'percentage' | 'kelly';
    value: number;
    maxRiskPerTrade: number;
  };
  stopLoss: {
    enabled: boolean;
    type: 'fixed' | 'atr' | 'percentage';
    value: number;
  };
  takeProfit: {
    enabled: boolean;
    type: 'fixed' | 'atr' | 'riskReward';
    value: number;
  };
  maxDrawdown: number;
  strategies: {
    eurusd: {
      enabled: boolean;
      confidence: number;
      parameters: {
        rsiPeriod: number;
        maPeriod: number;
        stopLoss: number;
        takeProfit: number;
      };
    };
    gold: {
      enabled: boolean;
      confidence: number;
      parameters: {
        volatilityThreshold: number;
        trendStrength: number;
        stopLoss: number;
        takeProfit: number;
      };
    };
  };
  notifications: {
    tradeAlerts: boolean;
    performanceUpdates: boolean;
    errorAlerts: boolean;
    systemStatus: boolean;
    email: boolean;
    push: boolean;
  };
  connection: {
    accountId: string;
    region: string;
    timeout: number;
    retryAttempts: number;
  };
}

const BotSettings: React.FC = () => {
  const { bot, updateBotConfig } = useTradingBot();
  const [activeTab, setActiveTab] = useState<string>('trading');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false);

  // Default extended configuration
  const [config, setConfig] = useState<ExtendedBotConfig>({
    symbols: ['EURUSD', 'XAUUSD'],
    riskPerTrade: 0.02,
    maxOpenTrades: 3,
    enabled: false,
    tradingHours: {
      start: '08:00',
      end: '17:00',
      timezone: 'UTC'
    },
    positionSizing: {
      method: 'percentage',
      value: 2,
      maxRiskPerTrade: 5
    },
    stopLoss: {
      enabled: true,
      type: 'percentage',
      value: 2
    },
    takeProfit: {
      enabled: true,
      type: 'riskReward',
      value: 2
    },
    maxDrawdown: 10,
    strategies: {
      eurusd: {
        enabled: true,
        confidence: 75,
        parameters: {
          rsiPeriod: 14,
          maPeriod: 20,
          stopLoss: 20,
          takeProfit: 40
        }
      },
      gold: {
        enabled: true,
        confidence: 70,
        parameters: {
          volatilityThreshold: 0.5,
          trendStrength: 0.7,
          stopLoss: 30,
          takeProfit: 60
        }
      }
    },
    notifications: {
      tradeAlerts: true,
      performanceUpdates: true,
      errorAlerts: true,
      systemStatus: false,
      email: false,
      push: true
    },
    connection: {
      accountId: '',
      region: 'new-york',
      timeout: 30000,
      retryAttempts: 3
    }
  });

  // Load current bot configuration and saved extended config
  useEffect(() => {
    // Load extended config from localStorage
    const savedExtendedConfig = localStorage.getItem('botExtendedConfig');
    if (savedExtendedConfig) {
      try {
        const parsedConfig = JSON.parse(savedExtendedConfig);
        setConfig(prev => ({
          ...prev,
          ...parsedConfig
        }));
      } catch (error) {
        console.error('Failed to load saved configuration:', error);
      }
    }

    // Load current bot configuration
    if (bot) {
      const currentConfig = bot.getConfig();
      setConfig(prev => ({
        ...prev,
        ...currentConfig
      }));
    }
  }, [bot]);

  // Handle configuration changes
  const handleConfigChange = useCallback((section: string, key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof ExtendedBotConfig],
        [key]: value
      }
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Handle direct config changes
  const handleDirectConfigChange = useCallback((key: keyof ExtendedBotConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
    setHasUnsavedChanges(true);
  }, []);

  // Save configuration
  const handleSaveConfig = useCallback(async () => {
    try {
      // Save to localStorage
      localStorage.setItem('botExtendedConfig', JSON.stringify(config));
      
      // Update bot configuration
      if (bot) {
        await updateBotConfig(config);
      }
      
      setHasUnsavedChanges(false);
      setShowSaveConfirmation(true);
      setTimeout(() => setShowSaveConfirmation(false), 3000);
    } catch (error) {
      console.error('Failed to save configuration:', error);
    }
  }, [config, bot, updateBotConfig]);

  const tabs = [
    { id: 'trading', label: 'Trading Parameters', icon: TrendingUp },
    { id: 'risk', label: 'Risk Management', icon: Shield },
    { id: 'strategies', label: 'Strategies', icon: Brain },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'connection', label: 'Connection', icon: Database }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                <Settings className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Bot Settings</h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">Configure your trading bot parameters</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              {hasUnsavedChanges && (
                <div className="flex items-center space-x-2 text-amber-600 dark:text-amber-400">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="text-sm font-medium">Unsaved changes</span>
                </div>
              )}
              
              {showSaveConfirmation && (
                <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm font-medium">Settings saved!</span>
                </div>
              )}
              
              <button
                onClick={handleSaveConfig}
                disabled={!hasUnsavedChanges}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  hasUnsavedChanges
                    ? 'bg-blue-600 hover:bg-blue-700 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                }`}
              >
                <Save className="w-4 h-4" />
                <span>Save Settings</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                        : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          <div className="p-6">
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white capitalize">{activeTab} Settings</h3>
              <p className="text-gray-600 dark:text-gray-400">
                {activeTab === 'trading' && 'Configure basic trading parameters and position sizing.'}
                {activeTab === 'risk' && 'Set up risk management rules and stop loss/take profit settings.'}
                {activeTab === 'strategies' && 'Fine-tune strategy parameters and confidence thresholds.'}
                {activeTab === 'notifications' && 'Manage alerts and notification preferences.'}
                {activeTab === 'connection' && 'Configure MetaAPI connection settings.'}
              </p>
              
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <Info className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">Coming Soon</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                      The {activeTab} configuration interface is being developed. 
                      Advanced settings will be available in the next update.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BotSettings;
