import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Bot, 
  Shield, 
  Target, 
  Clock, 
  DollarSign, 
  Bell, 
  Database, 
  Download, 
  Upload, 
  Save, 
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Info,
  Zap,
  TrendingUp,
  BarChart3,
  Globe,
  Smartphone
} from 'lucide-react';
import { BotConfig } from '../ai/TradingBot';
import { useTradingBot } from '../hooks/useTradingBot';

interface BotSettingsProps {
  onConfigUpdate?: (config: Partial<BotConfig>) => void;
}

interface ExtendedBotConfig extends BotConfig {
  // Trading Parameters
  tradingHours: {
    start: string;
    end: string;
    timezone: string;
  };
  positionSizing: {
    method: 'fixed' | 'percentage' | 'kelly';
    value: number;
    maxRiskPerTrade: number;
  };
  
  // Risk Management
  stopLoss: {
    enabled: boolean;
    type: 'fixed' | 'atr' | 'percentage';
    value: number;
  };
  takeProfit: {
    enabled: boolean;
    type: 'fixed' | 'atr' | 'riskReward';
    value: number;
  };
  maxDrawdown: number;
  
  // Strategy Settings
  strategies: {
    eurusd: {
      enabled: boolean;
      confidence: number;
      parameters: Record<string, any>;
    };
    gold: {
      enabled: boolean;
      confidence: number;
      parameters: Record<string, any>;
    };
  };
  
  // Notifications
  notifications: {
    tradeAlerts: boolean;
    performanceUpdates: boolean;
    errorAlerts: boolean;
    systemStatus: boolean;
    email: boolean;
    push: boolean;
  };
  
  // Connection Settings
  connection: {
    accountId: string;
    region: string;
    timeout: number;
    retryAttempts: number;
  };
}

const BotSettings: React.FC<BotSettingsProps> = ({ onConfigUpdate }) => {
  const { bot, botStatus, updateBotConfig, error } = useTradingBot();
  const [activeTab, setActiveTab] = useState('trading');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showSaveConfirm, setShowSaveConfirm] = useState(false);

  // Default extended configuration
  const [config, setConfig] = useState<ExtendedBotConfig>({
    enabled: false,
    symbols: ['EURUSD', 'XAUUSD'],
    riskLevel: 'balanced',
    maxTradesPerDay: 10,
    maxOpenPositions: 3,
    accountId: import.meta.env.VITE_MASTER_ACCOUNT_ID || '168683',
    
    tradingHours: {
      start: '08:00',
      end: '18:00',
      timezone: 'UTC'
    },
    positionSizing: {
      method: 'percentage',
      value: 2.0,
      maxRiskPerTrade: 1.0
    },
    stopLoss: {
      enabled: true,
      type: 'percentage',
      value: 2.0
    },
    takeProfit: {
      enabled: true,
      type: 'riskReward',
      value: 2.0
    },
    maxDrawdown: 10.0,
    strategies: {
      eurusd: {
        enabled: true,
        confidence: 75,
        parameters: {}
      },
      gold: {
        enabled: true,
        confidence: 70,
        parameters: {}
      }
    },
    notifications: {
      tradeAlerts: true,
      performanceUpdates: true,
      errorAlerts: true,
      systemStatus: true,
      email: false,
      push: true
    },
    connection: {
      accountId: import.meta.env.VITE_MASTER_ACCOUNT_ID || '168683',
      region: 'new-york',
      timeout: 30000,
      retryAttempts: 3
    }
  });

  // Load current bot configuration and saved extended config
  useEffect(() => {
    // Load extended config from localStorage
    const savedExtendedConfig = localStorage.getItem('botExtendedConfig');
    if (savedExtendedConfig) {
      try {
        const parsedConfig = JSON.parse(savedExtendedConfig);
        setConfig(prev => ({
          ...prev,
          ...parsedConfig
        }));
      } catch (error) {
        console.error('Failed to load saved configuration:', error);
      }
    }

    // Load current bot configuration
    if (bot) {
      const currentConfig = bot.getConfig();
      setConfig(prev => ({
        ...prev,
        ...currentConfig
      }));
    }
  }, [bot]);

  const handleConfigChange = (section: string, field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof ExtendedBotConfig],
        [field]: value
      }
    }));
    setHasUnsavedChanges(true);
  };

  const handleDirectConfigChange = (field: keyof BotConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
    setHasUnsavedChanges(true);
  };

  const saveConfiguration = () => {
    if (bot && onConfigUpdate) {
      const basicConfig: Partial<BotConfig> = {
        enabled: config.enabled,
        symbols: config.symbols,
        riskLevel: config.riskLevel,
        maxTradesPerDay: config.maxTradesPerDay,
        maxOpenPositions: config.maxOpenPositions,
        accountId: config.accountId
      };
      
      updateBotConfig(basicConfig);
      onConfigUpdate(basicConfig);
      
      // Save extended config to localStorage
      localStorage.setItem('botExtendedConfig', JSON.stringify(config));
      
      setHasUnsavedChanges(false);
      setShowSaveConfirm(true);
      setTimeout(() => setShowSaveConfirm(false), 3000);
    }
  };

  const resetToDefaults = () => {
    if (window.confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
      localStorage.removeItem('botExtendedConfig');
      window.location.reload();
    }
  };

  const exportConfiguration = () => {
    const dataStr = JSON.stringify(config, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `botforex-config-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const importConfiguration = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importedConfig = JSON.parse(e.target?.result as string);
          setConfig(importedConfig);
          setHasUnsavedChanges(true);
        } catch (error) {
          alert('Invalid configuration file format');
        }
      };
      reader.readAsText(file);
    }
  };

  const tabs = [
    { id: 'trading', name: 'Trading Parameters', icon: TrendingUp },
    { id: 'risk', name: 'Risk Management', icon: Shield },
    { id: 'strategies', name: 'Strategies', icon: Bot },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'connection', name: 'Connection', icon: Database }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Settings className="w-8 h-8" />
            <div>
              <h1 className="text-2xl font-bold">Bot Settings</h1>
              <p className="text-purple-100">Configure your trading bot parameters</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {hasUnsavedChanges && (
              <div className="flex items-center space-x-2 bg-yellow-500/20 px-3 py-2 rounded-lg">
                <AlertTriangle className="w-4 h-4 text-yellow-300" />
                <span className="text-sm text-yellow-100">Unsaved Changes</span>
              </div>
            )}
            
            {showSaveConfirm && (
              <div className="flex items-center space-x-2 bg-green-500/20 px-3 py-2 rounded-lg">
                <CheckCircle className="w-4 h-4 text-green-300" />
                <span className="text-sm text-green-100">Settings Saved</span>
              </div>
            )}
            
            <div className="flex items-center space-x-2">
              <button
                onClick={saveConfiguration}
                disabled={!hasUnsavedChanges}
                className="flex items-center space-x-2 px-4 py-2 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 rounded-lg transition-colors"
              >
                <Save className="w-4 h-4" />
                <span>Save</span>
              </button>
              
              <button
                onClick={resetToDefaults}
                className="flex items-center space-x-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 rounded-lg transition-colors"
              >
                <RotateCcw className="w-4 h-4" />
                <span>Reset</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Action Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              botStatus.isRunning ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                botStatus.isRunning ? 'bg-green-500' : 'bg-red-500'
              } animate-pulse`}></div>
              <span className={`text-sm font-medium ${
                botStatus.isRunning ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'
              }`}>
                {botStatus.isRunning ? 'BOT ACTIVE' : 'BOT STOPPED'}
              </span>
            </div>
            
            {error && (
              <div className="flex items-center space-x-2 px-3 py-2 bg-red-100 dark:bg-red-900 rounded-lg">
                <AlertTriangle className="w-4 h-4 text-red-600" />
                <span className="text-sm text-red-700 dark:text-red-300">Connection Error</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={exportConfiguration}
              className="flex items-center space-x-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              <Download className="w-4 h-4" />
              <span className="text-sm">Export</span>
            </button>
            
            <label className="flex items-center space-x-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors cursor-pointer">
              <Upload className="w-4 h-4" />
              <span className="text-sm">Import</span>
              <input
                type="file"
                accept=".json"
                onChange={importConfiguration}
                className="hidden"
              />
            </label>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'trading' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Trading Symbols */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                    <Globe className="w-5 h-5 text-blue-600" />
                    <span>Trading Symbols</span>
                  </h3>

                  <div className="space-y-3">
                    {['EURUSD', 'XAUUSD', 'GBPUSD', 'USDJPY', 'AUDUSD'].map((symbol) => (
                      <label key={symbol} className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={config.symbols.includes(symbol)}
                          onChange={(e) => {
                            const newSymbols = e.target.checked
                              ? [...config.symbols, symbol]
                              : config.symbols.filter(s => s !== symbol);
                            handleDirectConfigChange('symbols', newSymbols);
                          }}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                        />
                        <span className="text-gray-900 dark:text-white font-medium">{symbol}</span>
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {symbol === 'EURUSD' ? 'Euro/US Dollar' :
                           symbol === 'XAUUSD' ? 'Gold/US Dollar' :
                           symbol === 'GBPUSD' ? 'British Pound/US Dollar' :
                           symbol === 'USDJPY' ? 'US Dollar/Japanese Yen' :
                           'Australian Dollar/US Dollar'}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Position Limits */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                    <Target className="w-5 h-5 text-green-600" />
                    <span>Position Limits</span>
                  </h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Maximum Trades Per Day
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="100"
                        value={config.maxTradesPerDay}
                        onChange={(e) => handleDirectConfigChange('maxTradesPerDay', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Limit daily trading activity to manage risk
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Maximum Open Positions
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="20"
                        value={config.maxOpenPositions}
                        onChange={(e) => handleDirectConfigChange('maxOpenPositions', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Maximum concurrent positions allowed
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Trading Hours */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <Clock className="w-5 h-5 text-purple-600" />
                  <span>Trading Hours</span>
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Start Time
                    </label>
                    <input
                      type="time"
                      value={config.tradingHours.start}
                      onChange={(e) => handleConfigChange('tradingHours', 'start', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      End Time
                    </label>
                    <input
                      type="time"
                      value={config.tradingHours.end}
                      onChange={(e) => handleConfigChange('tradingHours', 'end', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Timezone
                    </label>
                    <select
                      value={config.tradingHours.timezone}
                      onChange={(e) => handleConfigChange('tradingHours', 'timezone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="UTC">UTC</option>
                      <option value="America/New_York">New York (EST/EDT)</option>
                      <option value="Europe/London">London (GMT/BST)</option>
                      <option value="Asia/Tokyo">Tokyo (JST)</option>
                      <option value="Australia/Sydney">Sydney (AEST/AEDT)</option>
                    </select>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <Info className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="text-sm text-blue-800 dark:text-blue-200 font-medium">Trading Hours Info</p>
                      <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                        The bot will only execute trades during these hours. Outside these hours, it will monitor markets but won't place new trades.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Position Sizing */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <DollarSign className="w-5 h-5 text-green-600" />
                  <span>Position Sizing</span>
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Sizing Method
                    </label>
                    <select
                      value={config.positionSizing.method}
                      onChange={(e) => handleConfigChange('positionSizing', 'method', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="fixed">Fixed Lot Size</option>
                      <option value="percentage">Percentage of Balance</option>
                      <option value="kelly">Kelly Criterion</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {config.positionSizing.method === 'fixed' ? 'Lot Size' :
                       config.positionSizing.method === 'percentage' ? 'Percentage (%)' :
                       'Kelly Multiplier'}
                    </label>
                    <input
                      type="number"
                      step={config.positionSizing.method === 'fixed' ? '0.01' : '0.1'}
                      min={config.positionSizing.method === 'fixed' ? '0.01' : '0.1'}
                      max={config.positionSizing.method === 'percentage' ? '10' : '2'}
                      value={config.positionSizing.value}
                      onChange={(e) => handleConfigChange('positionSizing', 'value', parseFloat(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Maximum Risk Per Trade (%)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    min="0.1"
                    max="5"
                    value={config.positionSizing.maxRiskPerTrade}
                    onChange={(e) => handleConfigChange('positionSizing', 'maxRiskPerTrade', parseFloat(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Maximum percentage of account balance to risk on a single trade
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'risk' && (
            <div className="space-y-6">
              {/* Risk Level Presets */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <Shield className="w-5 h-5 text-red-600" />
                  <span>Risk Level Presets</span>
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[
                    {
                      level: 'conservative',
                      name: 'Conservative',
                      description: 'Low risk, steady growth',
                      color: 'green',
                      maxRisk: 0.5,
                      maxDrawdown: 5
                    },
                    {
                      level: 'balanced',
                      name: 'Balanced',
                      description: 'Moderate risk, balanced returns',
                      color: 'blue',
                      maxRisk: 1.0,
                      maxDrawdown: 10
                    },
                    {
                      level: 'aggressive',
                      name: 'Aggressive',
                      description: 'Higher risk, higher potential returns',
                      color: 'red',
                      maxRisk: 2.0,
                      maxDrawdown: 20
                    }
                  ].map((preset) => (
                    <div
                      key={preset.level}
                      onClick={() => {
                        handleDirectConfigChange('riskLevel', preset.level);
                        handleConfigChange('positionSizing', 'maxRiskPerTrade', preset.maxRisk);
                        handleConfigChange('maxDrawdown', '', preset.maxDrawdown);
                      }}
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        config.riskLevel === preset.level
                          ? `border-${preset.color}-500 bg-${preset.color}-50 dark:bg-${preset.color}-900/20`
                          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                      }`}
                    >
                      <div className="text-center">
                        <h4 className={`font-semibold ${
                          config.riskLevel === preset.level
                            ? `text-${preset.color}-700 dark:text-${preset.color}-300`
                            : 'text-gray-900 dark:text-white'
                        }`}>
                          {preset.name}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {preset.description}
                        </p>
                        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                          <p>Max Risk: {preset.maxRisk}%</p>
                          <p>Max Drawdown: {preset.maxDrawdown}%</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Stop Loss Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <Target className="w-5 h-5 text-red-600" />
                  <span>Stop Loss Configuration</span>
                </h3>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-4">
                    <input
                      type="checkbox"
                      id="stopLossEnabled"
                      checked={config.stopLoss.enabled}
                      onChange={(e) => handleConfigChange('stopLoss', 'enabled', e.target.checked)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="stopLossEnabled" className="text-sm font-medium text-gray-900 dark:text-white">
                      Enable Automatic Stop Loss
                    </label>
                  </div>

                  {config.stopLoss.enabled && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Stop Loss Type
                        </label>
                        <select
                          value={config.stopLoss.type}
                          onChange={(e) => handleConfigChange('stopLoss', 'type', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        >
                          <option value="fixed">Fixed Pips</option>
                          <option value="percentage">Percentage</option>
                          <option value="atr">ATR Multiple</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          {config.stopLoss.type === 'fixed' ? 'Pips' :
                           config.stopLoss.type === 'percentage' ? 'Percentage (%)' :
                           'ATR Multiplier'}
                        </label>
                        <input
                          type="number"
                          step={config.stopLoss.type === 'fixed' ? '1' : '0.1'}
                          min={config.stopLoss.type === 'fixed' ? '5' : '0.1'}
                          value={config.stopLoss.value}
                          onChange={(e) => handleConfigChange('stopLoss', 'value', parseFloat(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Take Profit Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <Target className="w-5 h-5 text-green-600" />
                  <span>Take Profit Configuration</span>
                </h3>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-4">
                    <input
                      type="checkbox"
                      id="takeProfitEnabled"
                      checked={config.takeProfit.enabled}
                      onChange={(e) => handleConfigChange('takeProfit', 'enabled', e.target.checked)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="takeProfitEnabled" className="text-sm font-medium text-gray-900 dark:text-white">
                      Enable Automatic Take Profit
                    </label>
                  </div>

                  {config.takeProfit.enabled && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Take Profit Type
                        </label>
                        <select
                          value={config.takeProfit.type}
                          onChange={(e) => handleConfigChange('takeProfit', 'type', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        >
                          <option value="fixed">Fixed Pips</option>
                          <option value="riskReward">Risk:Reward Ratio</option>
                          <option value="atr">ATR Multiple</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          {config.takeProfit.type === 'fixed' ? 'Pips' :
                           config.takeProfit.type === 'riskReward' ? 'Risk:Reward Ratio' :
                           'ATR Multiplier'}
                        </label>
                        <input
                          type="number"
                          step={config.takeProfit.type === 'fixed' ? '1' : '0.1'}
                          min={config.takeProfit.type === 'fixed' ? '5' : '0.1'}
                          value={config.takeProfit.value}
                          onChange={(e) => handleConfigChange('takeProfit', 'value', parseFloat(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Maximum Drawdown */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-600" />
                  <span>Maximum Drawdown Protection</span>
                </h3>

                <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Maximum Drawdown (%)
                      </label>
                      <input
                        type="number"
                        step="0.5"
                        min="1"
                        max="50"
                        value={config.maxDrawdown}
                        onChange={(e) => setConfig(prev => ({ ...prev, maxDrawdown: parseFloat(e.target.value) }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Bot will stop trading if drawdown exceeds this percentage
                      </p>
                    </div>

                    <div className="flex items-center">
                      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 w-full">
                        <div className="text-center">
                          <p className="text-sm text-gray-600 dark:text-gray-400">Current Drawdown</p>
                          <p className="text-2xl font-bold text-yellow-600">0.0%</p>
                          <p className="text-xs text-gray-500 mt-1">Within safe limits</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 flex items-start space-x-3">
                    <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                    <div>
                      <p className="text-sm text-yellow-800 dark:text-yellow-200 font-medium">Drawdown Protection</p>
                      <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                        When maximum drawdown is reached, the bot will automatically stop trading to protect your capital.
                        You can manually restart trading after reviewing and adjusting your strategy.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'strategies' && (
            <div className="space-y-6">
              {/* EURUSD Strategy */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <Bot className="w-5 h-5 text-blue-600" />
                  <span>EURUSD Trend AI Strategy</span>
                </h3>

                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="eurusdEnabled"
                        checked={config.strategies.eurusd.enabled}
                        onChange={(e) => handleConfigChange('strategies', 'eurusd', {
                          ...config.strategies.eurusd,
                          enabled: e.target.checked
                        })}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <label htmlFor="eurusdEnabled" className="text-sm font-medium text-gray-900 dark:text-white">
                        Enable EURUSD Strategy
                      </label>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm text-gray-600 dark:text-gray-400">Performance</p>
                        <p className="text-lg font-bold text-green-600">+12.4%</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600 dark:text-gray-400">Win Rate</p>
                        <p className="text-lg font-bold text-blue-600">68%</p>
                      </div>
                    </div>
                  </div>

                  {config.strategies.eurusd.enabled && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Minimum Confidence Level (%)
                          </label>
                          <input
                            type="range"
                            min="50"
                            max="95"
                            value={config.strategies.eurusd.confidence}
                            onChange={(e) => handleConfigChange('strategies', 'eurusd', {
                              ...config.strategies.eurusd,
                              confidence: parseInt(e.target.value)
                            })}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                          />
                          <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>50%</span>
                            <span className="font-medium text-blue-600">{config.strategies.eurusd.confidence}%</span>
                            <span>95%</span>
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Higher confidence = fewer but more reliable signals
                          </p>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Strategy Status
                          </label>
                          <div className="bg-white dark:bg-gray-800 rounded-lg p-3">
                            <div className="flex items-center space-x-2">
                              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                              <span className="text-sm font-medium text-green-600">Active & Monitoring</span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">Last signal: 2 hours ago</p>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Strategy Parameters</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                              Trend Period
                            </label>
                            <select className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                              <option value="14">14 periods</option>
                              <option value="21">21 periods</option>
                              <option value="50">50 periods</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                              RSI Threshold
                            </label>
                            <input
                              type="number"
                              min="20"
                              max="80"
                              defaultValue="30"
                              className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            />
                          </div>

                          <div>
                            <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                              Volume Filter
                            </label>
                            <select className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                              <option value="low">Low</option>
                              <option value="medium">Medium</option>
                              <option value="high">High</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Gold Strategy */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-yellow-600" />
                  <span>Gold Sniper Pro Strategy</span>
                </h3>

                <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="goldEnabled"
                        checked={config.strategies.gold.enabled}
                        onChange={(e) => handleConfigChange('strategies', 'gold', {
                          ...config.strategies.gold,
                          enabled: e.target.checked
                        })}
                        className="w-4 h-4 text-yellow-600 bg-gray-100 border-gray-300 rounded focus:ring-yellow-500"
                      />
                      <label htmlFor="goldEnabled" className="text-sm font-medium text-gray-900 dark:text-white">
                        Enable Gold Strategy
                      </label>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm text-gray-600 dark:text-gray-400">Performance</p>
                        <p className="text-lg font-bold text-green-600">+8.7%</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600 dark:text-gray-400">Win Rate</p>
                        <p className="text-lg font-bold text-yellow-600">72%</p>
                      </div>
                    </div>
                  </div>

                  {config.strategies.gold.enabled && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Minimum Confidence Level (%)
                          </label>
                          <input
                            type="range"
                            min="50"
                            max="95"
                            value={config.strategies.gold.confidence}
                            onChange={(e) => handleConfigChange('strategies', 'gold', {
                              ...config.strategies.gold,
                              confidence: parseInt(e.target.value)
                            })}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                          />
                          <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>50%</span>
                            <span className="font-medium text-yellow-600">{config.strategies.gold.confidence}%</span>
                            <span>95%</span>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Strategy Status
                          </label>
                          <div className="bg-white dark:bg-gray-800 rounded-lg p-3">
                            <div className="flex items-center space-x-2">
                              <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                              <span className="text-sm font-medium text-yellow-600">Active & Monitoring</span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">Last signal: 45 minutes ago</p>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Strategy Parameters</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                              Volatility Filter
                            </label>
                            <select className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                              <option value="low">Low Volatility</option>
                              <option value="medium">Medium Volatility</option>
                              <option value="high">High Volatility</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                              News Impact
                            </label>
                            <select className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                              <option value="ignore">Ignore News</option>
                              <option value="consider">Consider News</option>
                              <option value="strict">Strict News Filter</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                              Market Session
                            </label>
                            <select className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                              <option value="all">All Sessions</option>
                              <option value="london">London Only</option>
                              <option value="newyork">New York Only</option>
                              <option value="overlap">Overlap Only</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Strategy Performance Summary */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
                  <BarChart3 className="w-5 h-5 text-purple-600" />
                  <span>Strategy Performance Summary</span>
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <p className="text-sm text-blue-600 dark:text-blue-400">Total Signals</p>
                    <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">247</p>
                    <p className="text-xs text-gray-500 mt-1">Last 30 days</p>
                  </div>

                  <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <p className="text-sm text-green-600 dark:text-green-400">Successful Trades</p>
                    <p className="text-2xl font-bold text-green-700 dark:text-green-300">172</p>
                    <p className="text-xs text-gray-500 mt-1">69.6% win rate</p>
                  </div>

                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <p className="text-sm text-purple-600 dark:text-purple-400">Avg. Confidence</p>
                    <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">78%</p>
                    <p className="text-xs text-gray-500 mt-1">Signal quality</p>
                  </div>

                  <div className="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                    <p className="text-sm text-yellow-600 dark:text-yellow-400">Profit Factor</p>
                    <p className="text-2xl font-bold text-yellow-700 dark:text-yellow-300">1.84</p>
                    <p className="text-xs text-gray-500 mt-1">Risk-adjusted</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-6">
              {/* Notification Types */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <Bell className="w-5 h-5 text-blue-600" />
                  <span>Notification Preferences</span>
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Trade Alerts */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <TrendingUp className="w-5 h-5 text-blue-600" />
                        <h4 className="font-medium text-gray-900 dark:text-white">Trade Alerts</h4>
                      </div>
                      <input
                        type="checkbox"
                        checked={config.notifications.tradeAlerts}
                        onChange={(e) => handleConfigChange('notifications', 'tradeAlerts', e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      Get notified when trades are opened, closed, or modified
                    </p>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" defaultChecked className="w-3 h-3 text-blue-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Trade opened</span>
                      </label>
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" defaultChecked className="w-3 h-3 text-blue-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Trade closed</span>
                      </label>
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" className="w-3 h-3 text-blue-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Stop loss hit</span>
                      </label>
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" className="w-3 h-3 text-blue-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Take profit hit</span>
                      </label>
                    </div>
                  </div>

                  {/* Performance Updates */}
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <BarChart3 className="w-5 h-5 text-green-600" />
                        <h4 className="font-medium text-gray-900 dark:text-white">Performance Updates</h4>
                      </div>
                      <input
                        type="checkbox"
                        checked={config.notifications.performanceUpdates}
                        onChange={(e) => handleConfigChange('notifications', 'performanceUpdates', e.target.checked)}
                        className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500"
                      />
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      Receive periodic performance summaries and milestones
                    </p>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" defaultChecked className="w-3 h-3 text-green-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Daily summary</span>
                      </label>
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" defaultChecked className="w-3 h-3 text-green-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Weekly report</span>
                      </label>
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" className="w-3 h-3 text-green-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Profit milestones</span>
                      </label>
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" defaultChecked className="w-3 h-3 text-green-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Drawdown warnings</span>
                      </label>
                    </div>
                  </div>

                  {/* Error Alerts */}
                  <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <AlertTriangle className="w-5 h-5 text-red-600" />
                        <h4 className="font-medium text-gray-900 dark:text-white">Error Alerts</h4>
                      </div>
                      <input
                        type="checkbox"
                        checked={config.notifications.errorAlerts}
                        onChange={(e) => handleConfigChange('notifications', 'errorAlerts', e.target.checked)}
                        className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
                      />
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      Get immediate alerts for system errors and connection issues
                    </p>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" defaultChecked className="w-3 h-3 text-red-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Connection errors</span>
                      </label>
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" defaultChecked className="w-3 h-3 text-red-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Trade execution errors</span>
                      </label>
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" className="w-3 h-3 text-red-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Strategy errors</span>
                      </label>
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" defaultChecked className="w-3 h-3 text-red-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Account issues</span>
                      </label>
                    </div>
                  </div>

                  {/* System Status */}
                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Settings className="w-5 h-5 text-purple-600" />
                        <h4 className="font-medium text-gray-900 dark:text-white">System Status</h4>
                      </div>
                      <input
                        type="checkbox"
                        checked={config.notifications.systemStatus}
                        onChange={(e) => handleConfigChange('notifications', 'systemStatus', e.target.checked)}
                        className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500"
                      />
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      Stay informed about bot status changes and system updates
                    </p>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" defaultChecked className="w-3 h-3 text-purple-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Bot started/stopped</span>
                      </label>
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" className="w-3 h-3 text-purple-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Strategy changes</span>
                      </label>
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" className="w-3 h-3 text-purple-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Settings updates</span>
                      </label>
                      <label className="flex items-center space-x-2 text-sm">
                        <input type="checkbox" className="w-3 h-3 text-purple-600 rounded" />
                        <span className="text-gray-700 dark:text-gray-300">Market sessions</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* Delivery Methods */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <Smartphone className="w-5 h-5 text-green-600" />
                  <span>Delivery Methods</span>
                </h3>

                <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Push Notifications */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Smartphone className="w-5 h-5 text-blue-600" />
                          <h4 className="font-medium text-gray-900 dark:text-white">Push Notifications</h4>
                        </div>
                        <input
                          type="checkbox"
                          checked={config.notifications.push}
                          onChange={(e) => handleConfigChange('notifications', 'push', e.target.checked)}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                        />
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Instant notifications to your browser or mobile device
                      </p>

                      {config.notifications.push && (
                        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <CheckCircle className="w-4 h-4 text-green-600" />
                            <span className="text-sm font-medium text-green-700 dark:text-green-300">
                              Push notifications enabled
                            </span>
                          </div>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            You'll receive real-time notifications in your browser
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Email Notifications */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Bell className="w-5 h-5 text-green-600" />
                          <h4 className="font-medium text-gray-900 dark:text-white">Email Notifications</h4>
                        </div>
                        <input
                          type="checkbox"
                          checked={config.notifications.email}
                          onChange={(e) => handleConfigChange('notifications', 'email', e.target.checked)}
                          className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500"
                        />
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Receive detailed reports and summaries via email
                      </p>

                      {config.notifications.email && (
                        <div className="space-y-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Email Address
                            </label>
                            <input
                              type="email"
                              placeholder="<EMAIL>"
                              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-green-500"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Email Frequency
                            </label>
                            <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                              <option value="immediate">Immediate</option>
                              <option value="hourly">Hourly Digest</option>
                              <option value="daily">Daily Summary</option>
                              <option value="weekly">Weekly Report</option>
                            </select>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Notification Test */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Test Notifications</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Send test notifications to verify your settings are working correctly
                </p>

                <div className="flex flex-wrap gap-3">
                  <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                    <Bell className="w-4 h-4" />
                    <span>Test Push</span>
                  </button>

                  <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                    <TrendingUp className="w-4 h-4" />
                    <span>Test Trade Alert</span>
                  </button>

                  <button className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                    <AlertTriangle className="w-4 h-4" />
                    <span>Test Error Alert</span>
                  </button>

                  {config.notifications.email && (
                    <button className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                      <Bell className="w-4 h-4" />
                      <span>Test Email</span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'connection' && (
            <div className="space-y-6">
              {/* MetaAPI Account Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <Database className="w-5 h-5 text-blue-600" />
                  <span>MetaAPI Account Configuration</span>
                </h3>

                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Account ID
                      </label>
                      <input
                        type="text"
                        value={config.connection.accountId}
                        onChange={(e) => handleConfigChange('connection', 'accountId', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter your MetaAPI account ID"
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Your MetaTrader account ID from MetaAPI dashboard
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Server Region
                      </label>
                      <select
                        value={config.connection.region}
                        onChange={(e) => handleConfigChange('connection', 'region', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="new-york">New York</option>
                        <option value="london">London</option>
                        <option value="singapore">Singapore</option>
                        <option value="sydney">Sydney</option>
                      </select>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Choose the region closest to your broker's server
                      </p>
                    </div>
                  </div>

                  <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Connection Timeout (ms)
                      </label>
                      <input
                        type="number"
                        min="5000"
                        max="60000"
                        step="1000"
                        value={config.connection.timeout}
                        onChange={(e) => handleConfigChange('connection', 'timeout', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Maximum time to wait for API responses
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Retry Attempts
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="10"
                        value={config.connection.retryAttempts}
                        onChange={(e) => handleConfigChange('connection', 'retryAttempts', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Number of retry attempts for failed connections
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Connection Status */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-green-600" />
                  <span>Connection Status</span>
                </h3>

                <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="flex items-center justify-center mb-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse mr-2"></div>
                        <span className="text-sm font-medium text-green-700 dark:text-green-300">Connected</span>
                      </div>
                      <p className="text-xs text-gray-500">MetaAPI Status</p>
                    </div>

                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-lg font-bold text-blue-700 dark:text-blue-300">47ms</p>
                      <p className="text-xs text-gray-500">Average Latency</p>
                    </div>

                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <p className="text-lg font-bold text-purple-700 dark:text-purple-300">99.8%</p>
                      <p className="text-xs text-gray-500">Uptime (24h)</p>
                    </div>
                  </div>

                  <div className="mt-6 flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">Last Connection Test</p>
                      <p className="text-xs text-gray-500">2 minutes ago - Successful</p>
                    </div>

                    <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                      <Zap className="w-4 h-4" />
                      <span>Test Connection</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Account Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <Info className="w-5 h-5 text-purple-600" />
                  <span>Account Information</span>
                </h3>

                <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Account Name</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">Demo Account #168683</p>
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Broker</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">MetaQuotes Demo</p>
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Server</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">MetaQuotes-Demo</p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Account Type</p>
                        <div className="flex items-center space-x-2">
                          <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium rounded">
                            DEMO
                          </span>
                          <span className="text-sm text-gray-600 dark:text-gray-400">Practice Account</span>
                        </div>
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Currency</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">USD</p>
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Leverage</p>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">1:100</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Advanced Connection Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
                  <Settings className="w-5 h-5 text-gray-600" />
                  <span>Advanced Settings</span>
                </h3>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">Auto-Reconnect</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Automatically reconnect on connection loss</p>
                      </div>
                      <input
                        type="checkbox"
                        defaultChecked
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">Keep-Alive Ping</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Send periodic pings to maintain connection</p>
                      </div>
                      <input
                        type="checkbox"
                        defaultChecked
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">Connection Logging</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Log connection events for debugging</p>
                      </div>
                      <input
                        type="checkbox"
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">Failover Mode</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Switch to backup servers on failure</p>
                      </div>
                      <input
                        type="checkbox"
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Connection Actions */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Connection Actions</h4>

                <div className="flex flex-wrap gap-3">
                  <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                    <CheckCircle className="w-4 h-4" />
                    <span>Reconnect</span>
                  </button>

                  <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                    <Zap className="w-4 h-4" />
                    <span>Test Latency</span>
                  </button>

                  <button className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                    <Database className="w-4 h-4" />
                    <span>Sync Account</span>
                  </button>

                  <button className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                    <Download className="w-4 h-4" />
                    <span>Download Logs</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BotSettings;
    </div>
  );
};

export default BotSettings;
