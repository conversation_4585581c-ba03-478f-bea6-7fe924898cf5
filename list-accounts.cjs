const MetaApi = require('metaapi.cloud-sdk').default;

async function listAccounts() {
  console.log('🔍 Listing all MetaAPI Accounts...\n');

  const token = process.env.VITE_METAAPI_TOKEN;

  if (!token) {
    console.log('❌ VITE_METAAPI_TOKEN not found in environment');
    return;
  }

  try {
    const api = new MetaApi(token);
    const accounts = await api.metatraderAccountApi.getAccountsWithInfiniteScrollPagination();

    console.log(`📋 Found ${accounts.length} account(s):\n`);

    if (accounts.length === 0) {
      console.log('❌ No accounts found!');
      console.log('💡 Please add an account in the MetaAPI dashboard first.');
      return;
    }

    accounts.forEach((account, index) => {
      console.log(`${index + 1}. Account ID: ${account.id}`);
      console.log(`   Name: ${account.name}`);
      console.log(`   Login: ${account.login}`);
      console.log(`   Server: ${account.server}`);
      console.log(`   Type: ${account.type || 'Not specified'}`);
      console.log(`   State: ${account.state}`);
      console.log(`   Connection: ${account.connectionStatus}`);
      
      const isDemo = account.server.toLowerCase().includes('demo') || 
                     account.type === 'demo' ||
                     account.name.toLowerCase().includes('demo');
      
      console.log(`   Account Type: ${isDemo ? '🧪 DEMO' : '💰 LIVE'}`);
      
      const canTrade = account.state === 'DEPLOYED' && account.connectionStatus === 'CONNECTED';
      console.log(`   Ready to Trade: ${canTrade ? '✅ YES' : '❌ NO'}`);
      
      if (!canTrade) {
        if (account.state !== 'DEPLOYED') {
          console.log(`   ⚠️  State issue: ${account.state} (needs DEPLOYED)`);
        }
        if (account.connectionStatus !== 'CONNECTED') {
          console.log(`   ⚠️  Connection issue: ${account.connectionStatus} (needs CONNECTED)`);
        }
      }
      
      console.log('');
    });

    // Find the best account to use
    const deployedAccounts = accounts.filter(acc => acc.state === 'DEPLOYED');
    const connectedAccounts = deployedAccounts.filter(acc => acc.connectionStatus === 'CONNECTED');
    
    console.log('🎯 Recommendations:');
    
    if (connectedAccounts.length > 0) {
      const recommended = connectedAccounts[0];
      console.log(`✅ Use Account ID: ${recommended.id}`);
      console.log(`   Name: ${recommended.name}`);
      console.log(`   Server: ${recommended.server}`);
      
      const isDemo = recommended.server.toLowerCase().includes('demo');
      console.log(`   Type: ${isDemo ? 'DEMO (safe for testing)' : 'LIVE (real money!)'}`);
      
      console.log(`\n💡 Update your .env file:`);
      console.log(`   VITE_MASTER_ACCOUNT_ID=${recommended.id}`);
      
    } else if (deployedAccounts.length > 0) {
      console.log(`⚠️  Found deployed accounts but none are connected:`);
      deployedAccounts.forEach(acc => {
        console.log(`   - ${acc.id}: ${acc.name} (${acc.connectionStatus})`);
      });
      console.log(`\n💡 Try connecting these accounts in MetaAPI dashboard`);
      
    } else {
      console.log(`❌ No deployed accounts found!`);
      console.log(`💡 Deploy an account in MetaAPI dashboard first`);
    }

  } catch (error) {
    console.error('❌ Failed to list accounts:', error.message);
  }
}

listAccounts().catch(console.error);
