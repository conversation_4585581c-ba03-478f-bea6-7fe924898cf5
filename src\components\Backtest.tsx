import React, { useState } from 'react';
import { Play, Settings, TrendingUp, TrendingDown, BarChart3, Calendar, DollarSign, Target, AlertTriangle, Download } from 'lucide-react';
import { getBacktestService, BacktestConfig, BacktestResults } from '../services/BacktestService';

const Backtest: React.FC = () => {
  const [config, setConfig] = useState<BacktestConfig>({
    symbol: 'EURUSD',
    strategy: 'eurusd',
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    endDate: new Date(),
    initialBalance: 10000,
    riskPerTrade: 2,
    maxOpenPositions: 3,
    timeframe: '1h',
    commission: 7, // $7 per lot
    spread: 1.5 // 1.5 pips
  });

  const [results, setResults] = useState<BacktestResults | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleRunBacktest = async () => {
    setIsRunning(true);
    setError(null);
    setResults(null);

    try {
      const backtestService = getBacktestService();
      const backtestResults = await backtestService.runBacktest(config);
      setResults(backtestResults);
    } catch (err) {
      console.error('Backtest failed:', err);
      setError(err instanceof Error ? err.message : 'Backtest failed');
    } finally {
      setIsRunning(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const getPerformanceColor = (value: number) => {
    return value >= 0 ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Strategy Backtesting</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Test your trading strategies against historical data
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleRunBacktest}
              disabled={isRunning}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-lg font-medium flex items-center space-x-2 transition-colors"
            >
              <Play className="w-4 h-4" />
              <span>{isRunning ? 'Running...' : 'Run Backtest'}</span>
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Settings className="w-5 h-5 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Configuration</h2>
            </div>

            <div className="space-y-4">
              {/* Symbol Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Symbol
                </label>
                <select
                  value={config.symbol}
                  onChange={(e) => setConfig({ ...config, symbol: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="EURUSD">EUR/USD</option>
                  <option value="XAUUSD">Gold (XAU/USD)</option>
                </select>
              </div>

              {/* Strategy Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Strategy
                </label>
                <select
                  value={config.strategy}
                  onChange={(e) => setConfig({ ...config, strategy: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="eurusd">EURUSD Strategy</option>
                  <option value="gold">Gold Strategy</option>
                  <option value="both">Both Strategies</option>
                </select>
              </div>

              {/* Date Range */}
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={config.startDate.toISOString().split('T')[0]}
                    onChange={(e) => setConfig({ ...config, startDate: new Date(e.target.value) })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={config.endDate.toISOString().split('T')[0]}
                    onChange={(e) => setConfig({ ...config, endDate: new Date(e.target.value) })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
              </div>

              {/* Timeframe */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Timeframe
                </label>
                <select
                  value={config.timeframe}
                  onChange={(e) => setConfig({ ...config, timeframe: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="1m">1 Minute</option>
                  <option value="5m">5 Minutes</option>
                  <option value="15m">15 Minutes</option>
                  <option value="1h">1 Hour</option>
                  <option value="4h">4 Hours</option>
                  <option value="1d">1 Day</option>
                </select>
              </div>

              {/* Initial Balance */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Initial Balance ($)
                </label>
                <input
                  type="number"
                  value={config.initialBalance}
                  onChange={(e) => setConfig({ ...config, initialBalance: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  min="1000"
                  step="1000"
                />
              </div>

              {/* Risk Per Trade */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Risk Per Trade (%)
                </label>
                <input
                  type="number"
                  value={config.riskPerTrade}
                  onChange={(e) => setConfig({ ...config, riskPerTrade: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  min="0.1"
                  max="10"
                  step="0.1"
                />
              </div>

              {/* Max Open Positions */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Max Open Positions
                </label>
                <input
                  type="number"
                  value={config.maxOpenPositions}
                  onChange={(e) => setConfig({ ...config, maxOpenPositions: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  min="1"
                  max="10"
                />
              </div>

              {/* Commission */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Commission per Lot ($)
                </label>
                <input
                  type="number"
                  value={config.commission}
                  onChange={(e) => setConfig({ ...config, commission: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  min="0"
                  step="0.1"
                />
              </div>

              {/* Spread */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Spread (pips)
                </label>
                <input
                  type="number"
                  value={config.spread}
                  onChange={(e) => setConfig({ ...config, spread: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  min="0.1"
                  step="0.1"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Results Panel */}
        <div className="lg:col-span-2">
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-red-600" />
                <span className="text-red-800 dark:text-red-200 font-medium">Backtest Error</span>
              </div>
              <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
            </div>
          )}

          {isRunning && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-blue-800 dark:text-blue-200">Running backtest...</p>
              <p className="text-blue-600 dark:text-blue-400 text-sm mt-1">This may take a few moments</p>
            </div>
          )}

          {results && (
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="w-5 h-5 text-green-600" />
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Net Profit</span>
                  </div>
                  <p className={`text-lg font-bold ${getPerformanceColor(results.summary.netProfit)}`}>
                    {formatCurrency(results.summary.netProfit)}
                  </p>
                  <p className={`text-sm ${getPerformanceColor(results.summary.returnPercent)}`}>
                    {formatPercent(results.summary.returnPercent)}
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                  <div className="flex items-center space-x-2">
                    <Target className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Win Rate</span>
                  </div>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {results.summary.winRate.toFixed(1)}%
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {results.summary.winningTrades}/{results.summary.totalTrades} trades
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                  <div className="flex items-center space-x-2">
                    <TrendingDown className="w-5 h-5 text-red-600" />
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Max Drawdown</span>
                  </div>
                  <p className="text-lg font-bold text-red-600">
                    {formatPercent(-results.summary.maxDrawdownPercent)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {formatCurrency(-results.summary.maxDrawdown)}
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="w-5 h-5 text-purple-600" />
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Profit Factor</span>
                  </div>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {results.summary.profitFactor.toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {results.summary.profitFactor >= 1.5 ? 'Excellent' : 
                     results.summary.profitFactor >= 1.2 ? 'Good' : 
                     results.summary.profitFactor >= 1.0 ? 'Fair' : 'Poor'}
                  </p>
                </div>
              </div>

              {/* Trade List */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Trades</h3>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-200 dark:border-gray-700">
                        <th className="text-left py-2 text-gray-600 dark:text-gray-400">Symbol</th>
                        <th className="text-left py-2 text-gray-600 dark:text-gray-400">Type</th>
                        <th className="text-left py-2 text-gray-600 dark:text-gray-400">Open Time</th>
                        <th className="text-left py-2 text-gray-600 dark:text-gray-400">Close Time</th>
                        <th className="text-right py-2 text-gray-600 dark:text-gray-400">Profit</th>
                        <th className="text-left py-2 text-gray-600 dark:text-gray-400">Exit Reason</th>
                      </tr>
                    </thead>
                    <tbody>
                      {results.trades.slice(-10).map((trade, index) => (
                        <tr key={trade.id} className="border-b border-gray-100 dark:border-gray-700">
                          <td className="py-2 text-gray-900 dark:text-white">{trade.symbol}</td>
                          <td className="py-2">
                            <span className={`px-2 py-1 rounded text-xs font-medium ${
                              trade.type === 'BUY'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                                : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                            }`}>
                              {trade.type}
                            </span>
                          </td>
                          <td className="py-2 text-gray-600 dark:text-gray-400">
                            {trade.openTime.toLocaleDateString()} {trade.openTime.toLocaleTimeString()}
                          </td>
                          <td className="py-2 text-gray-600 dark:text-gray-400">
                            {trade.closeTime.toLocaleDateString()} {trade.closeTime.toLocaleTimeString()}
                          </td>
                          <td className={`py-2 text-right font-medium ${getPerformanceColor(trade.profit)}`}>
                            {formatCurrency(trade.profit)}
                          </td>
                          <td className="py-2 text-gray-600 dark:text-gray-400 capitalize">
                            {trade.exitReason.replace('_', ' ')}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Detailed Statistics */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Detailed Statistics</h3>
                  <button className="text-blue-600 hover:text-blue-700 flex items-center space-x-1">
                    <Download className="w-4 h-4" />
                    <span>Export</span>
                  </button>
                </div>

                <div className="grid grid-cols-2 lg:grid-cols-3 gap-6">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Trading Performance</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Total Trades:</span>
                        <span className="text-gray-900 dark:text-white">{results.summary.totalTrades}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Winning Trades:</span>
                        <span className="text-green-600">{results.summary.winningTrades}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Losing Trades:</span>
                        <span className="text-red-600">{results.summary.losingTrades}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Win Rate:</span>
                        <span className="text-gray-900 dark:text-white">{results.summary.winRate.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Profit & Loss</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Gross Profit:</span>
                        <span className="text-green-600">{formatCurrency(results.summary.totalProfit)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Gross Loss:</span>
                        <span className="text-red-600">{formatCurrency(-results.summary.totalLoss)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Net Profit:</span>
                        <span className={getPerformanceColor(results.summary.netProfit)}>
                          {formatCurrency(results.summary.netProfit)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Profit Factor:</span>
                        <span className="text-gray-900 dark:text-white">{results.summary.profitFactor.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Risk Metrics</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Max Drawdown:</span>
                        <span className="text-red-600">{formatPercent(-results.summary.maxDrawdownPercent)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Average Win:</span>
                        <span className="text-green-600">{formatCurrency(results.summary.averageWin)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Average Loss:</span>
                        <span className="text-red-600">{formatCurrency(-results.summary.averageLoss)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Largest Win:</span>
                        <span className="text-green-600">{formatCurrency(results.summary.largestWin)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {!results && !isRunning && !error && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-12 text-center">
              <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Ready to Backtest</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Configure your parameters and click "Run Backtest" to analyze your strategy performance
              </p>
              <button
                onClick={handleRunBacktest}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium flex items-center space-x-2 mx-auto transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>Run Backtest</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Backtest;
