// Performance Optimization Test for BOTFOREX
// Run this with: node test-performance-optimizations.cjs

const MetaApi = require('metaapi.cloud-sdk').default;
require('dotenv').config();

async function testPerformanceOptimizations() {
  console.log('🚀 Testing BOTFOREX Performance Optimizations...\n');

  const startTime = Date.now();
  let testResults = {
    connectionTime: 0,
    cacheEfficiency: 0,
    rateLimitingWorking: false,
    errorRecoveryWorking: false,
    overallPerformance: 'Unknown'
  };

  try {
    // Test 1: Connection Performance
    console.log('1️⃣ Testing Connection Performance...');
    const connectionStart = Date.now();
    
    const token = process.env.VITE_METAAPI_TOKEN;
    const accountId = process.env.VITE_MASTER_ACCOUNT_ID;
    
    if (!token || token === 'your_actual_metaapi_token_here') {
      throw new Error('MetaAPI token not configured');
    }
    
    if (!accountId || accountId === 'YOUR_ACTUAL_METAAPI_ACCOUNT_ID') {
      throw new Error('Account ID not configured');
    }

    const api = new MetaApi(token);
    const account = await api.metatraderAccountApi.getAccount(accountId);
    
    if (account.state !== 'DEPLOYED') {
      throw new Error('Account not deployed');
    }

    const connection = account.getRPCConnection();
    await connection.connect();
    await connection.waitSynchronized();
    
    testResults.connectionTime = Date.now() - connectionStart;
    console.log(`✅ Connection established in ${testResults.connectionTime}ms`);

    // Test 2: Cache Efficiency
    console.log('\n2️⃣ Testing Cache Efficiency...');
    const cacheTestStart = Date.now();
    
    // First call (should hit API)
    const accountInfo1 = await connection.getAccountInformation();
    const firstCallTime = Date.now() - cacheTestStart;
    
    // Second call (should hit cache if implemented)
    const cacheCallStart = Date.now();
    const accountInfo2 = await connection.getAccountInformation();
    const secondCallTime = Date.now() - cacheCallStart;
    
    testResults.cacheEfficiency = ((firstCallTime - secondCallTime) / firstCallTime) * 100;
    console.log(`✅ First call: ${firstCallTime}ms, Second call: ${secondCallTime}ms`);
    console.log(`✅ Cache efficiency: ${testResults.cacheEfficiency.toFixed(1)}%`);

    // Test 3: Rate Limiting
    console.log('\n3️⃣ Testing Rate Limiting...');
    const rateLimitStart = Date.now();
    
    // Make multiple rapid calls
    const rapidCalls = [];
    for (let i = 0; i < 5; i++) {
      rapidCalls.push(connection.getSymbolPrice('EURUSD'));
    }
    
    try {
      await Promise.all(rapidCalls);
      const rateLimitTime = Date.now() - rateLimitStart;
      testResults.rateLimitingWorking = rateLimitTime > 500; // Should take at least 500ms with rate limiting
      console.log(`✅ Rate limiting test completed in ${rateLimitTime}ms`);
      console.log(`✅ Rate limiting ${testResults.rateLimitingWorking ? 'working' : 'needs improvement'}`);
    } catch (error) {
      if (error.message.includes('rate limit') || error.message.includes('TooManyRequests')) {
        testResults.rateLimitingWorking = true;
        console.log('✅ Rate limiting working (caught rate limit error)');
      } else {
        console.log('⚠️ Rate limiting test inconclusive:', error.message);
      }
    }

    // Test 4: Error Recovery
    console.log('\n4️⃣ Testing Error Recovery...');
    
    try {
      // Try to get data for non-existent symbol (should trigger error recovery)
      await connection.getSymbolPrice('INVALID_SYMBOL');
    } catch (error) {
      testResults.errorRecoveryWorking = true;
      console.log('✅ Error recovery working (handled invalid symbol gracefully)');
    }

    // Test 5: Market Data Performance
    console.log('\n5️⃣ Testing Market Data Performance...');
    const marketDataStart = Date.now();
    
    const symbols = ['EURUSD', 'XAUUSD', 'GBPUSD', 'USDJPY'];
    const marketDataPromises = symbols.map(symbol => 
      connection.getSymbolPrice(symbol).catch(err => ({ symbol, error: err.message }))
    );
    
    const marketDataResults = await Promise.all(marketDataPromises);
    const marketDataTime = Date.now() - marketDataStart;
    
    const successfulCalls = marketDataResults.filter(result => !result.error).length;
    console.log(`✅ Retrieved ${successfulCalls}/${symbols.length} market data points in ${marketDataTime}ms`);
    console.log(`✅ Average time per symbol: ${(marketDataTime / symbols.length).toFixed(1)}ms`);

    // Test 6: Position Data Performance
    console.log('\n6️⃣ Testing Position Data Performance...');
    const positionsStart = Date.now();
    
    try {
      const positions = await connection.getPositions();
      const positionsTime = Date.now() - positionsStart;
      console.log(`✅ Retrieved ${positions.length} positions in ${positionsTime}ms`);
    } catch (error) {
      console.log('⚠️ Position data test failed:', error.message);
    }

    // Test 7: Historical Data Performance
    console.log('\n7️⃣ Testing Historical Data Performance...');
    const historicalStart = Date.now();
    
    try {
      const candles = await connection.getCandles('EURUSD', '1h', 100);
      const historicalTime = Date.now() - historicalStart;
      console.log(`✅ Retrieved ${candles.length} historical candles in ${historicalTime}ms`);
    } catch (error) {
      console.log('⚠️ Historical data test failed:', error.message);
    }

    // Calculate Overall Performance Score
    const totalTime = Date.now() - startTime;
    let performanceScore = 0;
    
    if (testResults.connectionTime < 5000) performanceScore += 25;
    if (testResults.cacheEfficiency > 50) performanceScore += 25;
    if (testResults.rateLimitingWorking) performanceScore += 25;
    if (testResults.errorRecoveryWorking) performanceScore += 25;
    
    if (performanceScore >= 90) testResults.overallPerformance = 'Excellent';
    else if (performanceScore >= 70) testResults.overallPerformance = 'Good';
    else if (performanceScore >= 50) testResults.overallPerformance = 'Fair';
    else testResults.overallPerformance = 'Needs Improvement';

    // Final Results
    console.log('\n📊 PERFORMANCE TEST RESULTS');
    console.log('================================');
    console.log(`Connection Time: ${testResults.connectionTime}ms`);
    console.log(`Cache Efficiency: ${testResults.cacheEfficiency.toFixed(1)}%`);
    console.log(`Rate Limiting: ${testResults.rateLimitingWorking ? '✅ Working' : '❌ Not Working'}`);
    console.log(`Error Recovery: ${testResults.errorRecoveryWorking ? '✅ Working' : '❌ Not Working'}`);
    console.log(`Overall Performance: ${testResults.overallPerformance} (${performanceScore}/100)`);
    console.log(`Total Test Time: ${totalTime}ms`);

    // Performance Recommendations
    console.log('\n💡 PERFORMANCE RECOMMENDATIONS');
    console.log('================================');
    
    if (testResults.connectionTime > 5000) {
      console.log('⚠️ Connection time is slow. Consider optimizing network settings.');
    }
    
    if (testResults.cacheEfficiency < 50) {
      console.log('⚠️ Cache efficiency is low. Verify caching implementation.');
    }
    
    if (!testResults.rateLimitingWorking) {
      console.log('⚠️ Rate limiting not detected. Implement request throttling.');
    }
    
    if (!testResults.errorRecoveryWorking) {
      console.log('⚠️ Error recovery not working. Implement retry mechanisms.');
    }

    if (performanceScore >= 75) {
      console.log('🎉 Performance optimizations are working well!');
      console.log('✅ Your BOTFOREX application is ready for real data trading.');
    } else {
      console.log('🔧 Some optimizations need attention before production use.');
    }

    // Cleanup
    await connection.close();
    console.log('\n✅ Test completed successfully');
    return true;

  } catch (error) {
    console.log('\n❌ Performance test failed:', error.message);
    
    if (error.message.includes('not found')) {
      console.log('💡 Make sure your account ID is correct and the account exists.');
    } else if (error.message.includes('not deployed')) {
      console.log('💡 Deploy your account in the MetaAPI dashboard first.');
    } else if (error.message.includes('not connected')) {
      console.log('💡 Check your account credentials and broker connection.');
    }
    
    return false;
  }
}

// Run the performance test
testPerformanceOptimizations().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test execution failed:', error);
  process.exit(1);
});
