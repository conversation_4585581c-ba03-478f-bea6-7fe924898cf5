// Error Recovery Service for BOTFOREX
// Handles automatic retry, fallback data, and graceful degradation

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

export interface FallbackData {
  marketData?: { [symbol: string]: any };
  accountInfo?: any;
  positions?: any[];
  timestamp: number;
}

export class ErrorRecoveryService {
  private static instance: ErrorRecoveryService | null = null;
  private fallbackData: FallbackData = { timestamp: 0 };
  private retryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2
  };

  public static getInstance(): ErrorRecoveryService {
    if (!ErrorRecoveryService.instance) {
      ErrorRecoveryService.instance = new ErrorRecoveryService();
    }
    return ErrorRecoveryService.instance;
  }

  // Retry with exponential backoff
  async retryWithBackoff<T>(
    operation: () => Promise<T>,
    context: string,
    config?: Partial<RetryConfig>
  ): Promise<T> {
    const finalConfig = { ...this.retryConfig, ...config };
    let lastError: Error;

    for (let attempt = 0; attempt <= finalConfig.maxRetries; attempt++) {
      try {
        const result = await operation();
        if (attempt > 0) {
          console.log(`✅ ${context} succeeded after ${attempt} retries`);
        }
        return result;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === finalConfig.maxRetries) {
          console.error(`❌ ${context} failed after ${finalConfig.maxRetries} retries:`, lastError);
          break;
        }

        const delay = Math.min(
          finalConfig.baseDelay * Math.pow(finalConfig.backoffMultiplier, attempt),
          finalConfig.maxDelay
        );

        console.warn(`⚠️ ${context} failed (attempt ${attempt + 1}/${finalConfig.maxRetries + 1}), retrying in ${delay}ms:`, lastError.message);
        
        await this.sleep(delay);
      }
    }

    throw lastError!;
  }

  // Store fallback data
  storeFallbackData(type: keyof FallbackData, data: any): void {
    if (type === 'timestamp') return;
    
    this.fallbackData[type] = data;
    this.fallbackData.timestamp = Date.now();
    
    console.log(`💾 Stored fallback data for ${type}`);
  }

  // Get fallback data
  getFallbackData<T>(type: keyof FallbackData, maxAge: number = 300000): T | null {
    if (type === 'timestamp') return null;
    
    const data = this.fallbackData[type];
    const age = Date.now() - this.fallbackData.timestamp;
    
    if (data && age <= maxAge) {
      console.log(`📦 Using fallback data for ${type} (age: ${Math.round(age / 1000)}s)`);
      return data as T;
    }
    
    return null;
  }

  // Check if error is recoverable
  isRecoverableError(error: Error): boolean {
    const recoverablePatterns = [
      'timeout',
      'network',
      'rate limit',
      'too many requests',
      'connection',
      'temporary',
      'service unavailable',
      'gateway timeout'
    ];

    const errorMessage = error.message.toLowerCase();
    return recoverablePatterns.some(pattern => errorMessage.includes(pattern));
  }

  // Get error severity
  getErrorSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    const errorMessage = error.message.toLowerCase();
    
    if (errorMessage.includes('rate limit') || errorMessage.includes('timeout')) {
      return 'low';
    }
    
    if (errorMessage.includes('network') || errorMessage.includes('connection')) {
      return 'medium';
    }
    
    if (errorMessage.includes('authentication') || errorMessage.includes('unauthorized')) {
      return 'high';
    }
    
    if (errorMessage.includes('not found') || errorMessage.includes('invalid account')) {
      return 'critical';
    }
    
    return 'medium';
  }

  // Create user-friendly error message
  getUserFriendlyError(error: Error): string {
    const errorMessage = error.message.toLowerCase();
    
    if (errorMessage.includes('rate limit')) {
      return 'API rate limit reached. Please wait a moment and try again.';
    }
    
    if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
      return 'Network connection issue. Please check your internet connection.';
    }
    
    if (errorMessage.includes('authentication') || errorMessage.includes('unauthorized')) {
      return 'Authentication failed. Please check your API credentials.';
    }
    
    if (errorMessage.includes('not found') || errorMessage.includes('invalid account')) {
      return 'Account not found. Please verify your account ID configuration.';
    }
    
    if (errorMessage.includes('not deployed')) {
      return 'Account not deployed. Please deploy your account in MetaAPI dashboard.';
    }
    
    if (errorMessage.includes('not connected')) {
      return 'Account not connected to broker. Please check your account settings.';
    }
    
    return 'An unexpected error occurred. Please try again later.';
  }

  // Execute with fallback
  async executeWithFallback<T>(
    operation: () => Promise<T>,
    fallbackType: keyof FallbackData,
    context: string,
    storeSuccess: boolean = true
  ): Promise<T> {
    try {
      const result = await this.retryWithBackoff(operation, context);
      
      if (storeSuccess) {
        this.storeFallbackData(fallbackType, result);
      }
      
      return result;
    } catch (error) {
      console.error(`${context} failed, attempting fallback:`, error);
      
      const fallbackData = this.getFallbackData<T>(fallbackType);
      if (fallbackData) {
        return fallbackData;
      }
      
      throw error;
    }
  }

  // Circuit breaker pattern
  private circuitBreakers: Map<string, {
    failures: number;
    lastFailure: number;
    state: 'closed' | 'open' | 'half-open';
  }> = new Map();

  async executeWithCircuitBreaker<T>(
    operation: () => Promise<T>,
    circuitName: string,
    context: string
  ): Promise<T> {
    const circuit = this.circuitBreakers.get(circuitName) || {
      failures: 0,
      lastFailure: 0,
      state: 'closed' as const
    };

    // Check if circuit is open
    if (circuit.state === 'open') {
      const timeSinceLastFailure = Date.now() - circuit.lastFailure;
      if (timeSinceLastFailure < 60000) { // 1 minute timeout
        throw new Error(`Circuit breaker is open for ${circuitName}`);
      } else {
        circuit.state = 'half-open';
      }
    }

    try {
      const result = await operation();
      
      // Success - reset circuit
      circuit.failures = 0;
      circuit.state = 'closed';
      this.circuitBreakers.set(circuitName, circuit);
      
      return result;
    } catch (error) {
      circuit.failures++;
      circuit.lastFailure = Date.now();
      
      if (circuit.failures >= 5) {
        circuit.state = 'open';
        console.warn(`🔴 Circuit breaker opened for ${circuitName}`);
      }
      
      this.circuitBreakers.set(circuitName, circuit);
      throw error;
    }
  }

  // Utility method for sleep
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get recovery statistics
  getRecoveryStats(): any {
    return {
      fallbackDataAge: Date.now() - this.fallbackData.timestamp,
      hasFallbackData: {
        marketData: !!this.fallbackData.marketData,
        accountInfo: !!this.fallbackData.accountInfo,
        positions: !!this.fallbackData.positions
      },
      circuitBreakers: Array.from(this.circuitBreakers.entries()).map(([name, circuit]) => ({
        name,
        state: circuit.state,
        failures: circuit.failures,
        lastFailure: circuit.lastFailure
      }))
    };
  }

  // Clear all fallback data
  clearFallbackData(): void {
    this.fallbackData = { timestamp: 0 };
    console.log('🗑️ Cleared all fallback data');
  }
}

// Export singleton instance getter
export const getErrorRecoveryService = () => ErrorRecoveryService.getInstance();
