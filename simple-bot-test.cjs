const MetaApi = require('metaapi.cloud-sdk').default;

async function simpleBotTest() {
  console.log('🤖 Simple Bot Test - Checking Basic Functionality...\n');

  const token = process.env.VITE_METAAPI_TOKEN;
  const accountId = process.env.VITE_MASTER_ACCOUNT_ID;

  console.log('📋 Environment Check:');
  console.log(`   Token: ${token ? 'Present ✅' : 'Missing ❌'}`);
  console.log(`   Account ID: ${accountId || 'Missing ❌'}`);
  console.log('');

  if (!token || !accountId) {
    console.log('❌ Environment variables missing - bot cannot work');
    return;
  }

  try {
    // Test 1: Basic API connection
    console.log('🔍 Test 1: Basic API Connection');
    const api = new MetaApi(token);
    const account = await api.metatraderAccountApi.getAccount(accountId);
    console.log(`   ✅ Account found: ${account.name}`);
    console.log(`   ✅ State: ${account.state}`);
    console.log(`   ✅ Connection: ${account.connectionStatus}`);
    
    // Test 2: Check if ready for trading
    console.log('\n🔍 Test 2: Trading Readiness');
    const isReady = account.state === 'DEPLOYED' && account.connectionStatus === 'CONNECTED';
    console.log(`   Ready for trading: ${isReady ? '✅ YES' : '❌ NO'}`);
    
    if (!isReady) {
      console.log('   ❌ Account not ready - this is why bot is not trading!');
      if (account.state !== 'DEPLOYED') {
        console.log(`   - State issue: ${account.state} (needs DEPLOYED)`);
      }
      if (account.connectionStatus !== 'CONNECTED') {
        console.log(`   - Connection issue: ${account.connectionStatus} (needs CONNECTED)`);
      }
      return;
    }
    
    // Test 3: Connection instance
    console.log('\n🔍 Test 3: Connection Instance');
    const connection = account.getStreamingConnection();
    console.log('   ✅ Connection instance created');
    
    // Test 4: Connect and sync
    console.log('\n🔍 Test 4: Connect and Synchronize');
    await connection.connect();
    console.log('   ✅ Connected to MetaAPI');
    
    await connection.waitSynchronized();
    console.log('   ✅ Synchronized with broker');
    
    // Test 5: Basic market data
    console.log('\n🔍 Test 5: Market Data Access');
    try {
      const price = await connection.getSymbolPrice('EURUSD');
      console.log(`   ✅ EURUSD price: ${price.bid}/${price.ask}`);
    } catch (priceError) {
      console.log(`   ❌ Price error: ${priceError.message}`);
    }
    
    // Test 6: Account information
    console.log('\n🔍 Test 6: Account Information');
    try {
      const accountInfo = await connection.getAccountInformation();
      console.log(`   ✅ Balance: ${accountInfo.balance} ${accountInfo.currency}`);
      console.log(`   ✅ Equity: ${accountInfo.equity} ${accountInfo.currency}`);
    } catch (infoError) {
      console.log(`   ❌ Account info error: ${infoError.message}`);
    }
    
    // Test 7: Current positions
    console.log('\n🔍 Test 7: Current Positions');
    try {
      const positions = await connection.getPositions();
      console.log(`   ✅ Open positions: ${positions.length}`);
      if (positions.length > 0) {
        positions.forEach(pos => {
          console.log(`     - ${pos.symbol}: ${pos.type} ${pos.volume} @ ${pos.openPrice}`);
        });
      }
    } catch (posError) {
      console.log(`   ❌ Positions error: ${posError.message}`);
    }
    
    console.log('\n🎯 Test Results Summary:');
    console.log('   ✅ MetaAPI connection working');
    console.log('   ✅ Account accessible');
    console.log('   ✅ Market data available');
    console.log('   ✅ Ready for bot trading');
    
    console.log('\n💡 If bot still not trading in browser:');
    console.log('   1. Check browser console for errors');
    console.log('   2. Verify bot is initialized (should see "Trading bot initialized successfully")');
    console.log('   3. Check if bot is started (should see "Trading Bot started successfully")');
    console.log('   4. Look for trading cycle logs (should see "🔄 Trading Cycle" every 30 seconds)');
    console.log('   5. Check confidence levels (should be generating signals with >65% confidence)');
    
    await connection.close();
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('not found')) {
      console.log('\n💡 Account ID issue - run: node -r dotenv/config list-accounts.cjs');
    } else if (error.message.includes('not deployed')) {
      console.log('\n💡 Account not deployed - check MetaAPI dashboard');
    } else if (error.message.includes('not connected')) {
      console.log('\n💡 Account not connected - check broker connection');
    }
  }
}

simpleBotTest().catch(console.error);
