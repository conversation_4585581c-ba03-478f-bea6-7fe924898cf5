// Backtesting Service for BOTFOREX
// Simulates trading strategies against historical data

import { getMetaApiService, MarketData } from './MetaApiService';
import { EURUSDStrategy } from '../ai/EURUSDStrategy';
import { GoldStrategy } from '../ai/GoldStrategy';
import { CandleData } from '../ai/TechnicalIndicators';

export interface BacktestConfig {
  symbol: string;
  strategy: 'eurusd' | 'gold' | 'both';
  startDate: Date;
  endDate: Date;
  initialBalance: number;
  riskPerTrade: number;
  maxOpenPositions: number;
  timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d';
  commission: number; // per lot
  spread: number; // in pips
}

export interface BacktestTrade {
  id: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  openTime: Date;
  closeTime: Date;
  openPrice: number;
  closePrice: number;
  volume: number;
  profit: number;
  commission: number;
  swap: number;
  strategy: string;
  confidence: number;
  stopLoss?: number;
  takeProfit?: number;
  exitReason: 'stop_loss' | 'take_profit' | 'strategy_exit' | 'max_time';
}

export interface BacktestResults {
  config: BacktestConfig;
  trades: BacktestTrade[];
  summary: {
    totalTrades: number;
    winningTrades: number;
    losingTrades: number;
    winRate: number;
    totalProfit: number;
    totalLoss: number;
    netProfit: number;
    maxDrawdown: number;
    maxDrawdownPercent: number;
    profitFactor: number;
    sharpeRatio: number;
    averageWin: number;
    averageLoss: number;
    largestWin: number;
    largestLoss: number;
    consecutiveWins: number;
    consecutiveLosses: number;
    finalBalance: number;
    returnPercent: number;
  };
  equity: Array<{ time: Date; balance: number; equity: number; drawdown: number }>;
  monthlyReturns: Array<{ month: string; return: number; trades: number }>;
}

export class BacktestService {
  private static instance: BacktestService | null = null;
  private eurusdStrategy = new EURUSDStrategy();
  private goldStrategy = new GoldStrategy();

  public static getInstance(): BacktestService {
    if (!BacktestService.instance) {
      BacktestService.instance = new BacktestService();
    }
    return BacktestService.instance;
  }

  async runBacktest(config: BacktestConfig): Promise<BacktestResults> {
    console.log('🔄 Starting backtest...', config);

    try {
      // Get historical data
      const historicalData = await this.getHistoricalData(config);
      
      if (historicalData.length === 0) {
        throw new Error('No historical data available for the specified period');
      }

      // Initialize backtest state
      let balance = config.initialBalance;
      let equity = config.initialBalance;
      let maxEquity = config.initialBalance;
      let maxDrawdown = 0;
      let maxDrawdownPercent = 0;
      
      const trades: BacktestTrade[] = [];
      const openPositions: Map<string, BacktestTrade> = new Map();
      const equityCurve: Array<{ time: Date; balance: number; equity: number; drawdown: number }> = [];
      
      let tradeId = 1;

      // Process each candle
      for (let i = 50; i < historicalData.length; i++) { // Start at 50 to have enough data for indicators
        const currentCandle = historicalData[i];
        const historicalSlice = historicalData.slice(0, i + 1);

        // Update open positions
        this.updateOpenPositions(openPositions, currentCandle, trades);

        // Check for new signals if we have room for more positions
        if (openPositions.size < config.maxOpenPositions) {
          const signals = await this.getStrategySignals(config, historicalSlice);
          
          for (const signal of signals) {
            if (openPositions.size >= config.maxOpenPositions) break;

            const trade = this.openPosition(
              tradeId++,
              signal,
              currentCandle,
              config,
              balance
            );

            if (trade) {
              openPositions.set(trade.id, trade);
            }
          }
        }

        // Calculate current equity
        equity = balance;
        for (const [, position] of openPositions) {
          const unrealizedPnL = this.calculateUnrealizedPnL(position, currentCandle);
          equity += unrealizedPnL;
        }

        // Track drawdown
        if (equity > maxEquity) {
          maxEquity = equity;
        }
        
        const currentDrawdown = maxEquity - equity;
        const currentDrawdownPercent = (currentDrawdown / maxEquity) * 100;
        
        if (currentDrawdown > maxDrawdown) {
          maxDrawdown = currentDrawdown;
        }
        
        if (currentDrawdownPercent > maxDrawdownPercent) {
          maxDrawdownPercent = currentDrawdownPercent;
        }

        // Record equity point (every hour for performance)
        if (i % 60 === 0 || i === historicalData.length - 1) {
          equityCurve.push({
            time: currentCandle.time,
            balance,
            equity,
            drawdown: currentDrawdownPercent
          });
        }

        // Update balance when trades close
        for (const trade of trades) {
          if (trade.closeTime.getTime() === currentCandle.time.getTime()) {
            balance += trade.profit + trade.commission + trade.swap;
          }
        }
      }

      // Close any remaining open positions
      const lastCandle = historicalData[historicalData.length - 1];
      for (const [, position] of openPositions) {
        this.closePosition(position, lastCandle, 'strategy_exit');
        trades.push(position);
        balance += position.profit + position.commission + position.swap;
      }

      // Calculate summary statistics
      const summary = this.calculateSummary(trades, config.initialBalance, balance, maxDrawdown, maxDrawdownPercent);
      
      // Calculate monthly returns
      const monthlyReturns = this.calculateMonthlyReturns(trades);

      return {
        config,
        trades,
        summary,
        equity: equityCurve,
        monthlyReturns
      };

    } catch (error) {
      console.error('Backtest failed:', error);
      throw error;
    }
  }

  private async getHistoricalData(config: BacktestConfig): Promise<CandleData[]> {
    try {
      const metaApiService = getMetaApiService();
      
      // Calculate number of candles needed
      const timeDiff = config.endDate.getTime() - config.startDate.getTime();
      const candleInterval = this.getTimeframeMinutes(config.timeframe) * 60 * 1000;
      const maxCandles = Math.min(Math.ceil(timeDiff / candleInterval), 10000); // Limit to 10k candles

      const candles = await metaApiService.getHistoricalData(
        config.symbol,
        config.timeframe,
        maxCandles,
        config.startDate
      );

      return candles.filter(candle => 
        candle.time >= config.startDate && candle.time <= config.endDate
      );
    } catch (error) {
      console.error('Failed to get historical data:', error);
      // Return mock data for demo purposes
      return this.generateMockHistoricalData(config);
    }
  }

  private generateMockHistoricalData(config: BacktestConfig): CandleData[] {
    const candles: CandleData[] = [];
    const startTime = config.startDate.getTime();
    const endTime = config.endDate.getTime();
    const interval = this.getTimeframeMinutes(config.timeframe) * 60 * 1000;
    
    let basePrice = config.symbol === 'XAUUSD' ? 2000 : 1.1000;
    let currentTime = startTime;
    
    while (currentTime <= endTime) {
      const volatility = 0.001;
      const change = (Math.random() - 0.5) * volatility * basePrice;
      
      const open = basePrice;
      const close = basePrice + change;
      const high = Math.max(open, close) + Math.random() * volatility * basePrice * 0.5;
      const low = Math.min(open, close) - Math.random() * volatility * basePrice * 0.5;
      
      candles.push({
        time: new Date(currentTime),
        open,
        high,
        low,
        close,
        volume: Math.floor(Math.random() * 1000) + 100
      });
      
      basePrice = close;
      currentTime += interval;
    }
    
    return candles;
  }

  private getTimeframeMinutes(timeframe: string): number {
    const timeframes: { [key: string]: number } = {
      '1m': 1,
      '5m': 5,
      '15m': 15,
      '1h': 60,
      '4h': 240,
      '1d': 1440
    };
    return timeframes[timeframe] || 60;
  }

  private async getStrategySignals(config: BacktestConfig, historicalData: CandleData[]): Promise<Array<{
    type: 'BUY' | 'SELL';
    confidence: number;
    strategy: string;
    stopLoss?: number;
    takeProfit?: number;
  }>> {
    const signals = [];
    
    if (config.strategy === 'eurusd' || config.strategy === 'both') {
      if (config.symbol === 'EURUSD') {
        const eurusdSignal = await this.eurusdStrategy.analyze(historicalData);
        if (eurusdSignal.signal !== 'HOLD') {
          signals.push({
            type: eurusdSignal.signal as 'BUY' | 'SELL',
            confidence: eurusdSignal.confidence,
            strategy: 'EURUSD_Strategy',
            stopLoss: eurusdSignal.stopLoss,
            takeProfit: eurusdSignal.takeProfit
          });
        }
      }
    }
    
    if (config.strategy === 'gold' || config.strategy === 'both') {
      if (config.symbol === 'XAUUSD') {
        const goldSignal = await this.goldStrategy.analyze(historicalData);
        if (goldSignal.signal !== 'HOLD') {
          signals.push({
            type: goldSignal.signal as 'BUY' | 'SELL',
            confidence: goldSignal.confidence,
            strategy: 'Gold_Strategy',
            stopLoss: goldSignal.stopLoss,
            takeProfit: goldSignal.takeProfit
          });
        }
      }
    }
    
    return signals;
  }

  private openPosition(
    id: number,
    signal: any,
    candle: CandleData,
    config: BacktestConfig,
    balance: number
  ): BacktestTrade | null {
    const volume = this.calculatePositionSize(balance, config.riskPerTrade, candle.close);
    
    if (volume <= 0) return null;

    const spread = config.spread * (config.symbol === 'XAUUSD' ? 0.01 : 0.0001);
    const openPrice = signal.type === 'BUY' ? candle.close + spread/2 : candle.close - spread/2;
    
    return {
      id: id.toString(),
      symbol: config.symbol,
      type: signal.type,
      openTime: candle.time,
      closeTime: candle.time, // Will be updated when closed
      openPrice,
      closePrice: openPrice, // Will be updated when closed
      volume,
      profit: 0, // Will be calculated when closed
      commission: -config.commission * volume,
      swap: 0,
      strategy: signal.strategy,
      confidence: signal.confidence,
      stopLoss: signal.stopLoss,
      takeProfit: signal.takeProfit,
      exitReason: 'strategy_exit'
    };
  }

  private calculatePositionSize(balance: number, riskPercent: number, price: number): number {
    const riskAmount = balance * (riskPercent / 100);
    const stopLossDistance = price * 0.01; // 1% stop loss for calculation
    return Math.max(0.01, Math.min(1.0, riskAmount / stopLossDistance / 10)); // Simplified calculation
  }

  private updateOpenPositions(
    openPositions: Map<string, BacktestTrade>,
    candle: CandleData,
    closedTrades: BacktestTrade[]
  ): void {
    const toClose: string[] = [];
    
    for (const [id, position] of openPositions) {
      let shouldClose = false;
      let exitReason: BacktestTrade['exitReason'] = 'strategy_exit';
      
      // Check stop loss
      if (position.stopLoss) {
        if ((position.type === 'BUY' && candle.low <= position.stopLoss) ||
            (position.type === 'SELL' && candle.high >= position.stopLoss)) {
          shouldClose = true;
          exitReason = 'stop_loss';
        }
      }
      
      // Check take profit
      if (position.takeProfit && !shouldClose) {
        if ((position.type === 'BUY' && candle.high >= position.takeProfit) ||
            (position.type === 'SELL' && candle.low <= position.takeProfit)) {
          shouldClose = true;
          exitReason = 'take_profit';
        }
      }
      
      // Check max time (close after 24 hours for demo)
      const maxTime = 24 * 60 * 60 * 1000; // 24 hours
      if (candle.time.getTime() - position.openTime.getTime() > maxTime) {
        shouldClose = true;
        exitReason = 'max_time';
      }
      
      if (shouldClose) {
        this.closePosition(position, candle, exitReason);
        closedTrades.push(position);
        toClose.push(id);
      }
    }
    
    // Remove closed positions
    toClose.forEach(id => openPositions.delete(id));
  }

  private closePosition(position: BacktestTrade, candle: CandleData, exitReason: BacktestTrade['exitReason']): void {
    const spread = 0.0001; // Simplified spread
    position.closePrice = position.type === 'BUY' ? candle.close - spread/2 : candle.close + spread/2;
    position.closeTime = candle.time;
    position.exitReason = exitReason;
    
    // Calculate profit
    const priceDiff = position.type === 'BUY' 
      ? position.closePrice - position.openPrice
      : position.openPrice - position.closePrice;
    
    const pipValue = position.symbol === 'XAUUSD' ? 0.01 : 0.0001;
    position.profit = (priceDiff / pipValue) * position.volume * (position.symbol === 'XAUUSD' ? 1 : 10);
  }

  private calculateUnrealizedPnL(position: BacktestTrade, candle: CandleData): number {
    const currentPrice = position.type === 'BUY' ? candle.close : candle.close;
    const priceDiff = position.type === 'BUY' 
      ? currentPrice - position.openPrice
      : position.openPrice - currentPrice;
    
    const pipValue = position.symbol === 'XAUUSD' ? 0.01 : 0.0001;
    return (priceDiff / pipValue) * position.volume * (position.symbol === 'XAUUSD' ? 1 : 10);
  }

  private calculateSummary(trades: BacktestTrade[], initialBalance: number, finalBalance: number, maxDrawdown: number, maxDrawdownPercent: number): BacktestResults['summary'] {
    const winningTrades = trades.filter(t => t.profit > 0);
    const losingTrades = trades.filter(t => t.profit < 0);
    
    const totalProfit = winningTrades.reduce((sum, t) => sum + t.profit, 0);
    const totalLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.profit, 0));
    
    return {
      totalTrades: trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: trades.length > 0 ? (winningTrades.length / trades.length) * 100 : 0,
      totalProfit,
      totalLoss,
      netProfit: finalBalance - initialBalance,
      maxDrawdown,
      maxDrawdownPercent,
      profitFactor: totalLoss > 0 ? totalProfit / totalLoss : totalProfit > 0 ? 999 : 0,
      sharpeRatio: 0, // Simplified for now
      averageWin: winningTrades.length > 0 ? totalProfit / winningTrades.length : 0,
      averageLoss: losingTrades.length > 0 ? totalLoss / losingTrades.length : 0,
      largestWin: winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.profit)) : 0,
      largestLoss: losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.profit)) : 0,
      consecutiveWins: this.calculateMaxConsecutive(trades, true),
      consecutiveLosses: this.calculateMaxConsecutive(trades, false),
      finalBalance,
      returnPercent: ((finalBalance - initialBalance) / initialBalance) * 100
    };
  }

  private calculateMaxConsecutive(trades: BacktestTrade[], wins: boolean): number {
    let maxConsecutive = 0;
    let currentConsecutive = 0;
    
    for (const trade of trades) {
      const isWin = trade.profit > 0;
      if (isWin === wins) {
        currentConsecutive++;
        maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
      } else {
        currentConsecutive = 0;
      }
    }
    
    return maxConsecutive;
  }

  private calculateMonthlyReturns(trades: BacktestTrade[]): Array<{ month: string; return: number; trades: number }> {
    const monthlyData: { [key: string]: { profit: number; trades: number } } = {};
    
    trades.forEach(trade => {
      const monthKey = trade.closeTime.toISOString().substring(0, 7); // YYYY-MM
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { profit: 0, trades: 0 };
      }
      monthlyData[monthKey].profit += trade.profit;
      monthlyData[monthKey].trades++;
    });
    
    return Object.entries(monthlyData).map(([month, data]) => ({
      month,
      return: data.profit,
      trades: data.trades
    })).sort((a, b) => a.month.localeCompare(b.month));
  }
}

// Export singleton instance getter
export const getBacktestService = () => BacktestService.getInstance();
