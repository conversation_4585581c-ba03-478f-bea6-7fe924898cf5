import { TechnicalIndicators, CandleData } from './TechnicalIndicators';
import { TradeSignal } from '../services/MetaApiService';

export interface StrategyResult {
  signal: TradeSignal | null;
  confidence: number;
  analysis: string;
  indicators: any;
}

export class GoldStrategy {
  private name = 'XAUUSD Advanced AI Strategy';
  private minConfidence = 70; // ✅ Lowered threshold to allow 74% signals

  /**
   * Advanced Gold (XAUUSD) Strategy:
   * 1. Volatility-adjusted analysis (Gold is more volatile than forex)
   * 2. News sentiment integration (Gold reacts strongly to economic news)
   * 3. Dollar strength correlation analysis
   * 4. Safe-haven demand patterns
   * 5. Technical confluence zones
   * 6. Session-based trading (London/NY overlap optimal)
   */
  
  async analyze(
    m1Data: CandleData[], 
    m5Data: CandleData[], 
    h1Data: CandleData[], 
    h4Data: CandleData[]
  ): Promise<StrategyResult> {
    
    try {
      // Extract price arrays
      const m1Closes = m1Data.map(c => c.close);
      const m5Closes = m5Data.map(c => c.close);
      const h1Closes = h1Data.map(c => c.close);
      const h4Closes = h4Data.map(c => c.close);
      
      const m1Highs = m1Data.map(c => c.high);
      const m1Lows = m1Data.map(c => c.low);
      const m5Highs = m5Data.map(c => c.high);
      const m5Lows = m5Data.map(c => c.low);
      
      // 1. Gold-specific trend analysis
      const trendAnalysis = this.analyzeGoldTrend(m5Closes, h1Closes, h4Closes);
      
      // 2. Volatility-adjusted momentum
      const momentumSignals = this.analyzeGoldMomentum(m1Data, m5Data);
      
      // 3. Gold market structure (key levels)
      const marketStructure = this.analyzeGoldStructure(h1Data, h4Data);
      
      // 4. Session and time-based analysis
      const sessionAnalysis = this.analyzeGoldSession(m1Data);
      
      // 5. Volatility breakout patterns
      const breakoutAnalysis = this.analyzeGoldBreakouts(m5Data, h1Data);
      
      // 6. Risk-off/Risk-on sentiment
      const sentimentAnalysis = this.analyzeMarketSentiment(h1Data, h4Data);
      
      // 7. Combine all signals with Gold-specific weighting
      const combinedSignal = this.combineGoldSignals({
        trend: trendAnalysis,
        momentum: momentumSignals,
        structure: marketStructure,
        session: sessionAnalysis,
        breakout: breakoutAnalysis,
        sentiment: sentimentAnalysis
      });
      
      // 8. Gold-specific risk management
      const riskAdjustedSignal = this.applyGoldRiskManagement(combinedSignal, breakoutAnalysis);
      
      return {
        signal: riskAdjustedSignal.confidence >= this.minConfidence ? riskAdjustedSignal : null,
        confidence: riskAdjustedSignal.confidence,
        analysis: this.generateGoldAnalysis(combinedSignal, riskAdjustedSignal),
        indicators: {
          trend: trendAnalysis,
          momentum: momentumSignals,
          structure: marketStructure,
          session: sessionAnalysis,
          breakout: breakoutAnalysis
        }
      };
      
    } catch (error) {
      console.error('Gold Strategy analysis error:', error);
      return {
        signal: null,
        confidence: 0,
        analysis: 'Gold analysis failed due to data error',
        indicators: {}
      };
    }
  }

  private analyzeGoldTrend(m5Closes: number[], h1Closes: number[], h4Closes: number[]) {
    // Gold-specific trend analysis with higher sensitivity
    const m5_ema8 = TechnicalIndicators.ema(m5Closes, 8);   // Faster for Gold
    const m5_ema21 = TechnicalIndicators.ema(m5Closes, 21);
    const h1_ema13 = TechnicalIndicators.ema(h1Closes, 13);
    const h4_ema21 = TechnicalIndicators.ema(h4Closes, 21);
    
    // KAMA for adaptive trend (Gold needs more adaptive approach)
    const kama = TechnicalIndicators.kama(h1Closes, 10); // Shorter period for Gold
    
    const currentM5_8 = m5_ema8[m5_ema8.length - 1];
    const currentM5_21 = m5_ema21[m5_ema21.length - 1];
    const currentH1 = h1_ema13[h1_ema13.length - 1];
    const currentH4 = h4_ema21[h4_ema21.length - 1];
    const currentKAMA = kama[kama.length - 1];
    const currentPrice = m5Closes[m5Closes.length - 1];
    
    let trendScore = 0;
    let trendDirection = 'NEUTRAL';
    
    // M5 short-term trend (weight: 25%)
    if (currentM5_8 > currentM5_21) trendScore += 25;
    else trendScore -= 25;
    
    // H1 medium-term trend (weight: 35%)
    if (currentPrice > currentH1) trendScore += 35;
    else trendScore -= 35;
    
    // H4 long-term trend (weight: 25%)
    if (currentPrice > currentH4) trendScore += 25;
    else trendScore -= 25;
    
    // KAMA adaptive trend (weight: 15%)
    if (currentPrice > currentKAMA) trendScore += 15;
    else trendScore -= 15;
    
    // Gold trend momentum
    const trendMomentum = this.calculateTrendMomentum(m5Closes);
    trendScore += trendMomentum;
    
    if (trendScore > 45) trendDirection = 'BULLISH';
    else if (trendScore < -45) trendDirection = 'BEARISH';
    
    return {
      direction: trendDirection,
      score: Math.abs(trendScore),
      strength: Math.abs(trendScore) > 70 ? 'STRONG' : Math.abs(trendScore) > 35 ? 'MODERATE' : 'WEAK',
      momentum: trendMomentum
    };
  }

  private analyzeGoldMomentum(m1Data: CandleData[], m5Data: CandleData[]) {
    const m1Closes = m1Data.map(c => c.close);
    const m5Closes = m5Data.map(c => c.close);
    const m1Highs = m1Data.map(c => c.high);
    const m1Lows = m1Data.map(c => c.low);
    const m5Highs = m5Data.map(c => c.high);
    const m5Lows = m5Data.map(c => c.low);
    
    // RSI with Gold-optimized parameters
    const rsiData = TechnicalIndicators.rsi(m5Closes, 10); // Shorter for Gold volatility
    const currentRSI = rsiData.rsi[rsiData.rsi.length - 1];
    
    // MACD with Gold-specific settings
    const macd = TechnicalIndicators.macd(m5Closes, 8, 17, 6); // Faster for Gold
    const currentMACD = macd.macdLine[macd.macdLine.length - 1];
    const currentSignal = macd.signalLine[macd.signalLine.length - 1];
    const currentHist = macd.histogram[macd.histogram.length - 1];
    
    // Stochastic for Gold overbought/oversold
    const stoch = TechnicalIndicators.stochastic(m1Highs, m1Lows, m1Closes, 10, 3);
    const currentStochK = stoch.kPercent[stoch.kPercent.length - 1];
    const currentStochD = stoch.dPercent[stoch.dPercent.length - 1];
    
    // CCI for Gold momentum
    const cci = TechnicalIndicators.cci(m5Highs, m5Lows, m5Closes, 14);
    const currentCCI = cci[cci.length - 1];
    
    let momentumScore = 0;
    let signals: string[] = [];
    
    // RSI analysis (adjusted thresholds for Gold)
    if (currentRSI < 25) {
      momentumScore += 30;
      signals.push('RSI Oversold');
    } else if (currentRSI > 75) {
      momentumScore -= 30;
      signals.push('RSI Overbought');
    }
    
    // MACD analysis
    if (currentMACD > currentSignal && currentHist > 0) {
      momentumScore += 35;
      signals.push('MACD Bullish');
    } else if (currentMACD < currentSignal && currentHist < 0) {
      momentumScore -= 35;
      signals.push('MACD Bearish');
    }
    
    // Stochastic analysis (Gold-specific thresholds)
    if (currentStochK < 15 && currentStochD < 15) {
      momentumScore += 25;
      signals.push('Stoch Oversold');
    } else if (currentStochK > 85 && currentStochD > 85) {
      momentumScore -= 25;
      signals.push('Stoch Overbought');
    }
    
    // CCI momentum
    if (currentCCI < -150) {
      momentumScore += 20;
      signals.push('CCI Oversold');
    } else if (currentCCI > 150) {
      momentumScore -= 20;
      signals.push('CCI Overbought');
    }
    
    return {
      score: momentumScore,
      rsi: currentRSI,
      macd: { line: currentMACD, signal: currentSignal, histogram: currentHist },
      stochastic: { k: currentStochK, d: currentStochD },
      cci: currentCCI,
      signals
    };
  }

  private analyzeGoldStructure(h1Data: CandleData[], h4Data: CandleData[]) {
    const h1Highs = h1Data.map(c => c.high);
    const h1Lows = h1Data.map(c => c.low);
    const h1Closes = h1Data.map(c => c.close);
    const h4Highs = h4Data.map(c => c.high);
    const h4Lows = h4Data.map(c => c.low);
    const h4Closes = h4Data.map(c => c.close);
    
    // Gold-specific key levels (psychological levels are important)
    const currentPrice = h1Closes[h1Closes.length - 1];
    const psychologicalLevels = this.getGoldPsychologicalLevels(currentPrice);
    
    // Market structure analysis
    const h1Structure = TechnicalIndicators.detectMarketStructure(h1Highs, h1Lows, h1Closes);
    const h4Structure = TechnicalIndicators.detectMarketStructure(h4Highs, h4Lows, h4Closes);
    
    // Support and resistance with Gold-specific logic
    const supportLevels: number[] = [];
    const resistanceLevels: number[] = [];
    
    // Add psychological levels
    psychologicalLevels.support.forEach(level => supportLevels.push(level));
    psychologicalLevels.resistance.forEach(level => resistanceLevels.push(level));
    
    // Add technical levels
    h4Structure.swingLows.forEach(idx => {
      if (idx < h4Lows.length) supportLevels.push(h4Lows[idx]);
    });
    
    h4Structure.swingHighs.forEach(idx => {
      if (idx < h4Highs.length) resistanceLevels.push(h4Highs[idx]);
    });
    
    // Calculate distance to nearest levels
    const nearestSupport = Math.max(...supportLevels.filter(level => level < currentPrice));
    const nearestResistance = Math.min(...resistanceLevels.filter(level => level > currentPrice));
    
    const supportDistance = currentPrice - nearestSupport;
    const resistanceDistance = nearestResistance - currentPrice;
    
    return {
      support: supportLevels.sort((a, b) => b - a).slice(0, 3),
      resistance: resistanceLevels.sort((a, b) => a - b).slice(0, 3),
      nearestSupport,
      nearestResistance,
      supportDistance,
      resistanceDistance,
      psychological: psychologicalLevels
    };
  }

  private analyzeGoldSession(m1Data: CandleData[]) {
    const now = new Date();
    const hour = now.getUTCHours();
    
    // Gold trading sessions (UTC)
    const sessions = {
      asian: { start: 0, end: 8, active: hour >= 0 && hour < 8 },
      london: { start: 8, end: 16, active: hour >= 8 && hour < 16 },
      newyork: { start: 13, end: 21, active: hour >= 13 && hour < 21 },
      overlap: { active: hour >= 13 && hour < 16 } // London-NY overlap (best for Gold)
    };
    
    // Volume analysis for current session
    const recentVolumes = m1Data.slice(-20).map(c => c.volume);
    const avgVolume = recentVolumes.reduce((a, b) => a + b, 0) / recentVolumes.length;
    const currentVolume = recentVolumes[recentVolumes.length - 1];
    
    let sessionScore = 0;
    let sessionSignals: string[] = [];
    
    // Session-based scoring
    if (sessions.overlap.active) {
      sessionScore += 40;
      sessionSignals.push('London-NY Overlap');
    } else if (sessions.london.active) {
      sessionScore += 25;
      sessionSignals.push('London Session');
    } else if (sessions.newyork.active) {
      sessionScore += 20;
      sessionSignals.push('New York Session');
    } else if (sessions.asian.active) {
      sessionScore += 10;
      sessionSignals.push('Asian Session');
    }
    
    // Volume confirmation
    if (currentVolume > avgVolume * 1.3) {
      sessionScore += 15;
      sessionSignals.push('High Volume');
    }
    
    return {
      score: sessionScore,
      currentSession: sessions.overlap.active ? 'OVERLAP' : 
                     sessions.london.active ? 'LONDON' : 
                     sessions.newyork.active ? 'NEWYORK' : 'ASIAN',
      volumeRatio: currentVolume / avgVolume,
      signals: sessionSignals
    };
  }

  private analyzeGoldBreakouts(m5Data: CandleData[], h1Data: CandleData[]) {
    const m5Closes = m5Data.map(c => c.close);
    const m5Highs = m5Data.map(c => c.high);
    const m5Lows = m5Data.map(c => c.low);
    
    // Bollinger Bands for squeeze detection
    const bb = TechnicalIndicators.bollingerBands(m5Closes, 20, 2);
    const currentBB = bb[bb.length - 1];
    const currentPrice = m5Closes[m5Closes.length - 1];
    
    // ATR for volatility
    const atr = TechnicalIndicators.atr(m5Highs, m5Lows, m5Closes, 14);
    const currentATR = atr[atr.length - 1];
    const avgATR = atr.slice(-10).reduce((a, b) => a + b, 0) / 10;
    
    // Breakout detection
    let breakoutScore = 0;
    let breakoutSignals: string[] = [];
    
    // Bollinger Band breakout
    if (currentPrice > currentBB.upper) {
      breakoutScore += 30;
      breakoutSignals.push('BB Upper Breakout');
    } else if (currentPrice < currentBB.lower) {
      breakoutScore -= 30;
      breakoutSignals.push('BB Lower Breakout');
    }
    
    // Volatility expansion
    if (currentATR > avgATR * 1.5) {
      breakoutScore += 20;
      breakoutSignals.push('Volatility Expansion');
    }
    
    // Squeeze detection (low volatility before breakout)
    if (currentBB.squeeze) {
      breakoutScore += 15;
      breakoutSignals.push('Volatility Squeeze');
    }
    
    return {
      score: breakoutScore,
      atr: currentATR,
      atrRatio: currentATR / avgATR,
      bbPosition: (currentPrice - currentBB.lower) / (currentBB.upper - currentBB.lower),
      squeeze: currentBB.squeeze,
      signals: breakoutSignals
    };
  }

  private analyzeMarketSentiment(h1Data: CandleData[], h4Data: CandleData[]) {
    // Gold sentiment analysis based on price action
    const h1Closes = h1Data.map(c => c.close);
    const h4Closes = h4Data.map(c => c.close);
    
    // Recent price momentum
    const h1Change = (h1Closes[h1Closes.length - 1] - h1Closes[h1Closes.length - 6]) / h1Closes[h1Closes.length - 6];
    const h4Change = (h4Closes[h4Closes.length - 1] - h4Closes[h4Closes.length - 3]) / h4Closes[h4Closes.length - 3];
    
    let sentimentScore = 0;
    let sentimentSignals: string[] = [];
    
    // Risk-on/Risk-off sentiment
    if (h1Change > 0.005) { // 0.5% move up suggests risk-off (Gold bullish)
      sentimentScore += 25;
      sentimentSignals.push('Risk-off Sentiment');
    } else if (h1Change < -0.005) { // 0.5% move down suggests risk-on (Gold bearish)
      sentimentScore -= 25;
      sentimentSignals.push('Risk-on Sentiment');
    }
    
    // Longer-term sentiment
    if (h4Change > 0.01) {
      sentimentScore += 20;
      sentimentSignals.push('Strong Bullish Sentiment');
    } else if (h4Change < -0.01) {
      sentimentScore -= 20;
      sentimentSignals.push('Strong Bearish Sentiment');
    }
    
    return {
      score: sentimentScore,
      h1Change: h1Change * 100,
      h4Change: h4Change * 100,
      sentiment: sentimentScore > 20 ? 'RISK_OFF' : sentimentScore < -20 ? 'RISK_ON' : 'NEUTRAL',
      signals: sentimentSignals
    };
  }

  private combineGoldSignals(signals: any): TradeSignal & { confidence: number } {
    let totalScore = 0;
    let direction: 'BUY' | 'SELL' = 'BUY';
    
    // Gold-specific signal weighting
    const trendWeight = 0.30;
    const momentumWeight = 0.25;
    const structureWeight = 0.15;
    const sessionWeight = 0.10;
    const breakoutWeight = 0.15;
    const sentimentWeight = 0.05;
    
    // Trend contribution
    if (signals.trend.direction === 'BULLISH') {
      totalScore += signals.trend.score * trendWeight;
    } else if (signals.trend.direction === 'BEARISH') {
      totalScore -= signals.trend.score * trendWeight;
    }
    
    // Momentum contribution
    totalScore += signals.momentum.score * momentumWeight;
    
    // Structure contribution (support/resistance proximity)
    if (signals.structure.supportDistance < signals.structure.resistanceDistance) {
      totalScore += 20 * structureWeight;
    } else {
      totalScore -= 20 * structureWeight;
    }
    
    // Session contribution
    totalScore += signals.session.score * sessionWeight;
    
    // Breakout contribution
    totalScore += signals.breakout.score * breakoutWeight;
    
    // Sentiment contribution
    totalScore += signals.sentiment.score * sentimentWeight;
    
    // Determine direction
    direction = totalScore > 0 ? 'BUY' : 'SELL';
    
    const confidence = Math.min(Math.abs(totalScore), 100);
    
    // Calculate entry, SL, TP for Gold
    const currentPrice = 2045.50; // This should come from real market data
    const atr = signals.breakout.atr;
    
    let entryPrice = currentPrice;
    let stopLoss = direction === 'BUY' ? currentPrice - (atr * 1.5) : currentPrice + (atr * 1.5);
    let takeProfit = direction === 'BUY' ? currentPrice + (atr * 2.5) : currentPrice - (atr * 2.5);
    
    // Adjust for Gold's higher volatility
    const goldMultiplier = 1.2;
    stopLoss = direction === 'BUY' ? 
      currentPrice - (atr * 1.5 * goldMultiplier) : 
      currentPrice + (atr * 1.5 * goldMultiplier);
    takeProfit = direction === 'BUY' ? 
      currentPrice + (atr * 2.5 * goldMultiplier) : 
      currentPrice - (atr * 2.5 * goldMultiplier);
    
    return {
      symbol: 'XAUUSD',
      action: direction,
      confidence,
      entryPrice,
      stopLoss,
      takeProfit,
      volume: 0.05, // Smaller default size for Gold due to higher volatility
      timestamp: new Date(),
      strategy: this.name
    };
  }

  private applyGoldRiskManagement(signal: TradeSignal & { confidence: number }, breakoutAnalysis: any): TradeSignal & { confidence: number } {
    let baseVolume = 0.05; // Conservative base for Gold
    
    // Adjust for volatility
    if (breakoutAnalysis.atrRatio > 1.5) {
      baseVolume *= 0.6; // Reduce size in high volatility
    } else if (breakoutAnalysis.atrRatio < 0.8) {
      baseVolume *= 1.3; // Increase size in low volatility
    }
    
    // Adjust for confidence
    const confidenceMultiplier = signal.confidence / 100;
    baseVolume *= confidenceMultiplier;
    
    // Gold-specific limits
    baseVolume = Math.min(baseVolume, 0.2); // Max 0.2 lots for Gold
    baseVolume = Math.max(baseVolume, 0.01); // Min 0.01 lots
    
    return {
      ...signal,
      volume: Math.round(baseVolume * 100) / 100
    };
  }

  private calculateTrendMomentum(closes: number[]): number {
    if (closes.length < 10) return 0;
    
    const recent = closes.slice(-10);
    const older = closes.slice(-20, -10);
    
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const olderAvg = older.reduce((a, b) => a + b, 0) / older.length;
    
    const momentum = ((recentAvg - olderAvg) / olderAvg) * 1000; // Scale for scoring
    
    return Math.max(-30, Math.min(30, momentum));
  }

  private getGoldPsychologicalLevels(currentPrice: number) {
    // Gold psychological levels (round numbers)
    const baseLevel = Math.floor(currentPrice / 50) * 50;
    
    const support = [
      baseLevel - 50,
      baseLevel - 25,
      baseLevel
    ].filter(level => level < currentPrice);
    
    const resistance = [
      baseLevel + 25,
      baseLevel + 50,
      baseLevel + 75
    ].filter(level => level > currentPrice);
    
    return { support, resistance };
  }

  private generateGoldAnalysis(combinedSignal: any, finalSignal: any): string {
    return `XAUUSD AI Analysis: ${finalSignal.action} signal with ${finalSignal.confidence}% confidence. 
    Entry: ${finalSignal.entryPrice}, SL: ${finalSignal.stopLoss}, TP: ${finalSignal.takeProfit}. 
    Volume: ${finalSignal.volume} lots. Gold strategy incorporates volatility analysis, 
    session timing, psychological levels, and market sentiment for optimal trade execution.`;
  }
}