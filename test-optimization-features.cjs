// Test BOTFOREX Performance Optimization Features
// This tests the optimization code without requiring real MetaAPI connection

const fs = require('fs');
const path = require('path');

function testOptimizationFeatures() {
  console.log('🚀 Testing BOTFOREX Performance Optimization Features...\n');

  let testResults = {
    metaApiServiceOptimizations: false,
    errorRecoveryService: false,
    componentOptimizations: false,
    performanceMonitor: false,
    cacheImplementation: false,
    rateLimiting: false,
    overallScore: 0
  };

  try {
    // Test 1: MetaAPI Service Optimizations
    console.log('1️⃣ Testing MetaAPI Service Optimizations...');
    
    const metaApiServicePath = path.join(__dirname, 'src', 'services', 'MetaApiService.ts');
    if (fs.existsSync(metaApiServicePath)) {
      const metaApiContent = fs.readFileSync(metaApiServicePath, 'utf8');
      
      // Check for caching implementation
      const hasCaching = metaApiContent.includes('accountInfoCache') && 
                        metaApiContent.includes('positionsCache') && 
                        metaApiContent.includes('historicalDataCache');
      
      // Check for rate limiting
      const hasRateLimiting = metaApiContent.includes('rateLimitedCall') && 
                             metaApiContent.includes('requestQueue') && 
                             metaApiContent.includes('minApiInterval');
      
      // Check for connection optimization
      const hasConnectionOpt = metaApiContent.includes('isConnectionHealthy') && 
                              metaApiContent.includes('getConnectionStats') && 
                              metaApiContent.includes('clearCache');
      
      // Check for error recovery integration
      const hasErrorRecovery = metaApiContent.includes('errorRecoveryService') && 
                              metaApiContent.includes('executeWithFallback');
      
      testResults.metaApiServiceOptimizations = hasCaching && hasRateLimiting && hasConnectionOpt;
      testResults.cacheImplementation = hasCaching;
      testResults.rateLimiting = hasRateLimiting;
      
      console.log(`✅ Caching System: ${hasCaching ? 'Implemented' : 'Missing'}`);
      console.log(`✅ Rate Limiting: ${hasRateLimiting ? 'Implemented' : 'Missing'}`);
      console.log(`✅ Connection Optimization: ${hasConnectionOpt ? 'Implemented' : 'Missing'}`);
      console.log(`✅ Error Recovery Integration: ${hasErrorRecovery ? 'Implemented' : 'Missing'}`);
    } else {
      console.log('❌ MetaApiService.ts not found');
    }

    // Test 2: Error Recovery Service
    console.log('\n2️⃣ Testing Error Recovery Service...');
    
    const errorRecoveryPath = path.join(__dirname, 'src', 'services', 'ErrorRecoveryService.ts');
    if (fs.existsSync(errorRecoveryPath)) {
      const errorRecoveryContent = fs.readFileSync(errorRecoveryPath, 'utf8');
      
      const hasRetryLogic = errorRecoveryContent.includes('retryWithBackoff') && 
                           errorRecoveryContent.includes('maxRetries');
      
      const hasFallbackData = errorRecoveryContent.includes('storeFallbackData') && 
                             errorRecoveryContent.includes('getFallbackData');
      
      const hasCircuitBreaker = errorRecoveryContent.includes('executeWithCircuitBreaker') && 
                               errorRecoveryContent.includes('circuitBreakers');
      
      const hasErrorClassification = errorRecoveryContent.includes('isRecoverableError') && 
                                    errorRecoveryContent.includes('getErrorSeverity');
      
      testResults.errorRecoveryService = hasRetryLogic && hasFallbackData && hasCircuitBreaker;
      
      console.log(`✅ Retry Logic: ${hasRetryLogic ? 'Implemented' : 'Missing'}`);
      console.log(`✅ Fallback Data: ${hasFallbackData ? 'Implemented' : 'Missing'}`);
      console.log(`✅ Circuit Breaker: ${hasCircuitBreaker ? 'Implemented' : 'Missing'}`);
      console.log(`✅ Error Classification: ${hasErrorClassification ? 'Implemented' : 'Missing'}`);
    } else {
      console.log('❌ ErrorRecoveryService.ts not found');
    }

    // Test 3: Component Optimizations
    console.log('\n3️⃣ Testing Component Optimizations...');
    
    const componentsToCheck = [
      'MarketDataWidget.tsx',
      'LivePortfolio.tsx',
      'AccountOverview.tsx'
    ];
    
    let optimizedComponents = 0;
    
    componentsToCheck.forEach(componentName => {
      const componentPath = path.join(__dirname, 'src', 'components', componentName);
      if (fs.existsSync(componentPath)) {
        const componentContent = fs.readFileSync(componentPath, 'utf8');
        
        const hasRetryLogic = componentContent.includes('retryCount') && 
                             componentContent.includes('maxRetries');
        
        const hasServiceReadyCheck = componentContent.includes('isServiceReady');
        
        const hasMountedCheck = componentContent.includes('isMounted');
        
        const hasOptimizedInterval = componentContent.includes('3000') || 
                                    componentContent.includes('8000') || 
                                    componentContent.includes('15000');
        
        if (hasRetryLogic && hasServiceReadyCheck && hasMountedCheck) {
          optimizedComponents++;
          console.log(`✅ ${componentName}: Optimized`);
        } else {
          console.log(`⚠️ ${componentName}: Partially optimized`);
        }
      } else {
        console.log(`❌ ${componentName}: Not found`);
      }
    });
    
    testResults.componentOptimizations = optimizedComponents >= 2;

    // Test 4: Performance Monitor
    console.log('\n4️⃣ Testing Performance Monitor...');
    
    const performanceMonitorPath = path.join(__dirname, 'src', 'components', 'PerformanceMonitor.tsx');
    if (fs.existsSync(performanceMonitorPath)) {
      const performanceMonitorContent = fs.readFileSync(performanceMonitorPath, 'utf8');
      
      const hasConnectionStats = performanceMonitorContent.includes('getConnectionStats');
      const hasCacheStats = performanceMonitorContent.includes('cacheStats');
      const hasRealTimeUpdates = performanceMonitorContent.includes('setInterval');
      const hasToggleVisibility = performanceMonitorContent.includes('setIsVisible');
      
      testResults.performanceMonitor = hasConnectionStats && hasCacheStats && hasRealTimeUpdates;
      
      console.log(`✅ Connection Stats: ${hasConnectionStats ? 'Implemented' : 'Missing'}`);
      console.log(`✅ Cache Statistics: ${hasCacheStats ? 'Implemented' : 'Missing'}`);
      console.log(`✅ Real-time Updates: ${hasRealTimeUpdates ? 'Implemented' : 'Missing'}`);
      console.log(`✅ Toggle Visibility: ${hasToggleVisibility ? 'Implemented' : 'Missing'}`);
    } else {
      console.log('❌ PerformanceMonitor.tsx not found');
    }

    // Test 5: App Integration
    console.log('\n5️⃣ Testing App Integration...');
    
    const appPath = path.join(__dirname, 'src', 'App.tsx');
    if (fs.existsSync(appPath)) {
      const appContent = fs.readFileSync(appPath, 'utf8');
      
      const hasPerformanceMonitorImport = appContent.includes('PerformanceMonitor');
      const hasPerformanceMonitorComponent = appContent.includes('<PerformanceMonitor');
      
      console.log(`✅ Performance Monitor Import: ${hasPerformanceMonitorImport ? 'Added' : 'Missing'}`);
      console.log(`✅ Performance Monitor Component: ${hasPerformanceMonitorComponent ? 'Added' : 'Missing'}`);
    } else {
      console.log('❌ App.tsx not found');
    }

    // Calculate Overall Score
    let score = 0;
    if (testResults.metaApiServiceOptimizations) score += 25;
    if (testResults.errorRecoveryService) score += 25;
    if (testResults.componentOptimizations) score += 20;
    if (testResults.performanceMonitor) score += 15;
    if (testResults.cacheImplementation) score += 10;
    if (testResults.rateLimiting) score += 5;
    
    testResults.overallScore = score;

    // Final Results
    console.log('\n📊 OPTIMIZATION FEATURES TEST RESULTS');
    console.log('=====================================');
    console.log(`MetaAPI Service Optimizations: ${testResults.metaApiServiceOptimizations ? '✅ Pass' : '❌ Fail'}`);
    console.log(`Error Recovery Service: ${testResults.errorRecoveryService ? '✅ Pass' : '❌ Fail'}`);
    console.log(`Component Optimizations: ${testResults.componentOptimizations ? '✅ Pass' : '❌ Fail'}`);
    console.log(`Performance Monitor: ${testResults.performanceMonitor ? '✅ Pass' : '❌ Fail'}`);
    console.log(`Cache Implementation: ${testResults.cacheImplementation ? '✅ Pass' : '❌ Fail'}`);
    console.log(`Rate Limiting: ${testResults.rateLimiting ? '✅ Pass' : '❌ Fail'}`);
    console.log(`Overall Score: ${testResults.overallScore}/100`);

    // Performance Grade
    let grade = 'F';
    if (testResults.overallScore >= 90) grade = 'A+';
    else if (testResults.overallScore >= 80) grade = 'A';
    else if (testResults.overallScore >= 70) grade = 'B';
    else if (testResults.overallScore >= 60) grade = 'C';
    else if (testResults.overallScore >= 50) grade = 'D';

    console.log(`Performance Grade: ${grade}`);

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS');
    console.log('==================');
    
    if (testResults.overallScore >= 80) {
      console.log('🎉 Excellent! Your performance optimizations are comprehensive.');
      console.log('✅ Ready for real data trading with MetaAPI.');
      console.log('✅ All major optimization features are implemented.');
    } else if (testResults.overallScore >= 60) {
      console.log('👍 Good progress! Most optimizations are in place.');
      console.log('🔧 Consider implementing the missing features for better performance.');
    } else {
      console.log('⚠️ More work needed on performance optimizations.');
      console.log('🔧 Focus on implementing the core optimization features first.');
    }

    if (!testResults.cacheImplementation) {
      console.log('• Implement caching system to reduce API calls');
    }
    if (!testResults.rateLimiting) {
      console.log('• Add rate limiting to prevent quota exhaustion');
    }
    if (!testResults.errorRecoveryService) {
      console.log('• Implement error recovery and fallback mechanisms');
    }
    if (!testResults.componentOptimizations) {
      console.log('• Optimize React components for better performance');
    }

    console.log('\n✅ Optimization features test completed');
    return testResults.overallScore >= 70;

  } catch (error) {
    console.log('\n❌ Test failed:', error.message);
    return false;
  }
}

// Run the test
const success = testOptimizationFeatures();
process.exit(success ? 0 : 1);
