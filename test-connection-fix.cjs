const MetaApi = require('metaapi.cloud-sdk').default;

async function testConnectionFix() {
  console.log('🔧 Testing MetaAPI Connection Fix...\n');

  const token = process.env.VITE_METAAPI_TOKEN;
  const accountId = process.env.VITE_MASTER_ACCOUNT_ID;

  console.log('📋 Configuration:');
  console.log(`   Token: ${token ? 'Present' : 'Missing'}`);
  console.log(`   Account ID: ${accountId}`);
  console.log('');

  if (!token) {
    console.log('❌ VITE_METAAPI_TOKEN not found in environment');
    return;
  }

  if (!accountId) {
    console.log('❌ VITE_MASTER_ACCOUNT_ID not found in environment');
    return;
  }

  try {
    const api = new MetaApi(token);
    
    console.log('🔍 Testing direct account lookup...');
    const account = await api.metatraderAccountApi.getAccount(accountId);
    
    console.log('✅ Account found successfully!');
    console.log(`   ID: ${account.id}`);
    console.log(`   Name: ${account.name}`);
    console.log(`   Login: ${account.login}`);
    console.log(`   Server: ${account.server}`);
    console.log(`   State: ${account.state}`);
    console.log(`   Connection: ${account.connectionStatus}`);
    
    // Test connection
    if (account.state === 'DEPLOYED' && account.connectionStatus === 'CONNECTED') {
      console.log('\n✅ Account is ready for trading!');
      
      // Test getting a connection instance
      console.log('🔗 Testing connection instance...');
      const connection = account.getStreamingConnection();
      await connection.connect();
      
      console.log('✅ Connection instance created successfully!');
      console.log('🎯 MetaAPI connection is fully working!');
      
      await connection.close();
      
    } else {
      console.log('\n⚠️  Account found but not ready:');
      if (account.state !== 'DEPLOYED') {
        console.log(`   - State: ${account.state} (needs DEPLOYED)`);
      }
      if (account.connectionStatus !== 'CONNECTED') {
        console.log(`   - Connection: ${account.connectionStatus} (needs CONNECTED)`);
      }
    }

  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
    
    if (error.message.includes('not found')) {
      console.log('\n💡 The account ID might be incorrect.');
      console.log('   Run: node -r dotenv/config list-accounts.cjs');
      console.log('   to see available accounts and get the correct ID.');
    }
  }
}

testConnectionFix().catch(console.error);
