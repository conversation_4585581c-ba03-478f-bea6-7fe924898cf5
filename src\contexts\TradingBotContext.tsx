import React, { createContext, useContext, ReactNode } from 'react';
import { useTradingBot } from '../hooks/useTradingBot';

// Create the context with the same interface as useTradingBot
const TradingBotContext = createContext<ReturnType<typeof useTradingBot> | null>(null);

interface TradingBotProviderProps {
  children: ReactNode;
}

export const TradingBotProvider: React.FC<TradingBotProviderProps> = ({ children }) => {
  const tradingBotValue = useTradingBot();

  return (
    <TradingBotContext.Provider value={tradingBotValue}>
      {children}
    </TradingBotContext.Provider>
  );
};

export const useTradingBotContext = () => {
  const context = useContext(TradingBotContext);
  if (!context) {
    throw new Error('useTradingBotContext must be used within a TradingBotProvider');
  }
  return context;
};
