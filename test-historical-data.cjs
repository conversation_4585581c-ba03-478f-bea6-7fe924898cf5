// Test Historical Data Availability for BOTFOREX Backtest
// Run this with: node test-historical-data.cjs

const MetaApi = require('metaapi.cloud-sdk').default;
require('dotenv').config();

async function testHistoricalData() {
  console.log('🧪 Testing Historical Data Availability...\n');

  const token = process.env.VITE_METAAPI_TOKEN;
  const accountId = process.env.VITE_MASTER_ACCOUNT_ID;
  
  if (!token || !accountId) {
    console.error('❌ Missing environment variables');
    return false;
  }

  try {
    console.log('1️⃣ Connecting to MetaAPI...');
    const api = new MetaApi(token);
    const account = await api.metatraderAccountApi.getAccount(accountId);
    
    if (account.state !== 'DEPLOYED') {
      console.log('❌ Account not deployed');
      return false;
    }
    
    console.log('✅ Account connected:', account.login);

    console.log('\n2️⃣ Testing Historical Data Access...');
    const connection = account.getRPCConnection();
    await connection.connect();
    await connection.waitSynchronized();

    // Test different symbols and timeframes
    const testCases = [
      { symbol: 'EURUSD', timeframe: '1h', count: 100 },
      { symbol: 'XAUUSD', timeframe: '1h', count: 100 },
      { symbol: 'EURUSD', timeframe: '1d', count: 30 },
    ];

    let successCount = 0;
    
    for (const testCase of testCases) {
      try {
        console.log(`\n📊 Testing ${testCase.symbol} ${testCase.timeframe} (${testCase.count} candles)...`);
        
        const startTime = Date.now();
        const candles = await connection.getCandles(testCase.symbol, testCase.timeframe, testCase.count);
        const duration = Date.now() - startTime;
        
        if (candles && candles.length > 0) {
          console.log(`✅ Success: ${candles.length} candles retrieved in ${duration}ms`);
          console.log(`   Latest: ${candles[candles.length - 1].time} - Close: ${candles[candles.length - 1].close}`);
          console.log(`   Oldest: ${candles[0].time} - Close: ${candles[0].close}`);
          successCount++;
        } else {
          console.log('❌ No data returned');
        }
      } catch (error) {
        console.log(`❌ Failed: ${error.message}`);
      }
    }

    console.log('\n3️⃣ Testing Historical Data with Start Date...');
    try {
      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
      console.log(`📅 Requesting EURUSD 1h data from ${startDate.toISOString()}...`);
      
      const historicalCandles = await connection.getCandles('EURUSD', '1h', 100, startDate);
      
      if (historicalCandles && historicalCandles.length > 0) {
        console.log(`✅ Historical data: ${historicalCandles.length} candles`);
        console.log(`   From: ${historicalCandles[0].time}`);
        console.log(`   To: ${historicalCandles[historicalCandles.length - 1].time}`);
        successCount++;
      } else {
        console.log('❌ No historical data with start date');
      }
    } catch (error) {
      console.log(`❌ Historical data with start date failed: ${error.message}`);
    }

    await connection.close();

    console.log('\n📊 Results Summary:');
    console.log('='.repeat(50));
    console.log(`Successful tests: ${successCount}/${testCases.length + 1}`);
    
    if (successCount > 0) {
      console.log('✅ Real historical data is available!');
      console.log('🎯 Your backtest will use REAL market data');
      console.log('\n💡 Benefits of Real Data:');
      console.log('• Accurate price movements and volatility');
      console.log('• Real market gaps and weekend breaks');
      console.log('• Authentic spread and slippage simulation');
      console.log('• Reliable strategy performance validation');
    } else {
      console.log('❌ Real historical data not available');
      console.log('🔄 Your backtest will use mock data');
      console.log('\n💡 Mock Data Features:');
      console.log('• Realistic price movements with proper volatility');
      console.log('• Appropriate base prices for each symbol');
      console.log('• Consistent data for testing purposes');
      console.log('• Good for initial strategy development');
    }

    console.log('\n🚀 Recommendations:');
    if (successCount > 0) {
      console.log('1. Use real data for final strategy validation');
      console.log('2. Test with different timeframes and date ranges');
      console.log('3. Compare results with live trading performance');
      console.log('4. Consider longer backtesting periods for better insights');
    } else {
      console.log('1. Contact your broker about historical data access');
      console.log('2. Consider upgrading your MetaAPI subscription');
      console.log('3. Use mock data for initial strategy development');
      console.log('4. Validate strategies on demo account before live trading');
    }

    return successCount > 0;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

// Run the test
testHistoricalData()
  .then(hasRealData => {
    console.log(`\n🎯 Final Result: ${hasRealData ? 'REAL DATA AVAILABLE' : 'MOCK DATA ONLY'}`);
    process.exit(0);
  })
  .catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
