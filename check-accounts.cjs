// Check Available MetaAPI Accounts
// Run this with: node check-accounts.cjs

const MetaApi = require('metaapi.cloud-sdk').default;
require('dotenv').config();

async function checkAccounts() {
  console.log('🔍 Checking Available MetaAPI Accounts...\n');

  const token = process.env.VITE_METAAPI_TOKEN;
  
  if (!token) {
    console.error('❌ Missing VITE_METAAPI_TOKEN');
    return false;
  }

  try {
    console.log('1️⃣ Connecting to MetaAPI...');
    const api = new MetaApi(token);

    console.log('2️⃣ Fetching all accounts...');
    const accounts = await api.metatraderAccountApi.getAccounts();
    
    console.log(`\n📊 Found ${accounts.length} account(s):\n`);
    
    if (accounts.length === 0) {
      console.log('❌ No accounts found');
      console.log('\n💡 Possible reasons:');
      console.log('• Account not properly configured');
      console.log('• Token doesn\'t have access to accounts');
      console.log('• Account was deleted or suspended');
      return false;
    }

    for (let i = 0; i < accounts.length; i++) {
      const account = accounts[i];
      console.log(`Account ${i + 1}:`);
      console.log(`  ID: ${account.id}`);
      console.log(`  Login: ${account.login}`);
      console.log(`  Name: ${account.name}`);
      console.log(`  Server: ${account.server}`);
      console.log(`  Platform: ${account.platform}`);
      console.log(`  State: ${account.state}`);
      console.log(`  Type: ${account.type}`);
      console.log(`  Magic: ${account.magic || 'N/A'}`);
      console.log(`  Connection Status: ${account.connectionStatus}`);
      console.log('');
    }

    // Check if our configured account exists
    const configuredAccountId = process.env.VITE_MASTER_ACCOUNT_ID;
    const ourAccount = accounts.find(acc => acc.id === configuredAccountId);
    
    if (ourAccount) {
      console.log(`✅ Configured account ${configuredAccountId} found!`);
      console.log(`   Login: ${ourAccount.login}`);
      console.log(`   State: ${ourAccount.state}`);
      console.log(`   Server: ${ourAccount.server}`);
      
      if (ourAccount.state === 'DEPLOYED') {
        console.log('✅ Account is deployed and ready');
        
        // Test historical data access
        console.log('\n3️⃣ Testing historical data access...');
        try {
          const connection = ourAccount.getRPCConnection();
          await connection.connect();
          await connection.waitSynchronized({ timeoutInSeconds: 30 });
          
          console.log('✅ Connected successfully');
          
          // Try to get some historical data
          const candles = await connection.getCandles('EURUSD', '1h', 10);
          
          if (candles && candles.length > 0) {
            console.log(`✅ Historical data available: ${candles.length} candles`);
            console.log(`   Latest: ${candles[candles.length - 1].time} - ${candles[candles.length - 1].close}`);
            
            await connection.close();
            return true;
          } else {
            console.log('❌ No historical data returned');
            await connection.close();
            return false;
          }
          
        } catch (error) {
          console.log(`❌ Historical data test failed: ${error.message}`);
          return false;
        }
        
      } else {
        console.log(`❌ Account state is ${ourAccount.state}, not DEPLOYED`);
        return false;
      }
    } else {
      console.log(`❌ Configured account ${configuredAccountId} not found`);
      console.log('\n💡 Available account IDs:');
      accounts.forEach(acc => console.log(`   ${acc.id} (${acc.login})`));
      return false;
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    return false;
  }
}

// Run the check
checkAccounts()
  .then(hasData => {
    console.log(`\n🎯 Result: ${hasData ? 'REAL DATA AVAILABLE' : 'MOCK DATA ONLY'}`);
    
    if (hasData) {
      console.log('\n🚀 Your backtest will use REAL historical data!');
      console.log('• Accurate market movements');
      console.log('• Real volatility patterns');
      console.log('• Authentic trading conditions');
    } else {
      console.log('\n🔄 Your backtest will use mock data');
      console.log('• Still useful for strategy development');
      console.log('• Consistent testing environment');
      console.log('• Good for initial validation');
    }
    
    process.exit(0);
  })
  .catch(error => {
    console.error('Check failed:', error);
    process.exit(1);
  });
