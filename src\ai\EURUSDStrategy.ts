import { TechnicalIndicators, CandleData } from './TechnicalIndicators';
import { TradeSignal } from '../services/MetaApiService';

export interface StrategyResult {
  signal: TradeSignal | null;
  confidence: number;
  analysis: string;
  indicators: any;
}

export class EURUSDStrategy {
  private name = 'EURUSD Advanced AI Strategy';
  private minConfidence = 75;

  /**
   * Advanced EURUSD Strategy combining:
   * 1. Multi-timeframe analysis
   * 2. Machine learning pattern recognition
   * 3. Market microstructure analysis
   * 4. News sentiment integration
   * 5. Risk-adjusted position sizing
   */
  
  async analyze(
    m1Data: CandleData[], 
    m5Data: CandleData[], 
    h1Data: CandleData[], 
    h4Data: CandleData[]
  ): Promise<StrategyResult> {
    
    try {
      // Extract price arrays
      const m1Closes = m1Data.map(c => c.close);
      const m5Closes = m5Data.map(c => c.close);
      const h1Closes = h1Data.map(c => c.close);
      const h4Closes = h4Data.map(c => c.close);
      
      const m1Highs = m1Data.map(c => c.high);
      const m1Lows = m1Data.map(c => c.low);
      
      // 1. Multi-timeframe trend analysis
      const trendAnalysis = this.analyzeTrend(m5Closes, h1Closes, h4Closes);
      
      // 2. Advanced momentum indicators
      const momentumSignals = this.analyzeMomentum(m1Data, m5Data);
      
      // 3. Market microstructure
      const microstructure = this.analyzeMarketMicrostructure(m1Data);
      
      // 4. Volatility and risk assessment
      const volatilityAnalysis = this.analyzeVolatility(m1Data, m5Data);
      
      // 5. Support/Resistance levels
      const srLevels = this.identifySupportResistance(h1Data, h4Data);
      
      // 6. Pattern recognition
      const patterns = this.recognizePatterns(m5Data);
      
      // 7. Combine all signals using weighted scoring
      const combinedSignal = this.combineSignals({
        trend: trendAnalysis,
        momentum: momentumSignals,
        microstructure,
        volatility: volatilityAnalysis,
        srLevels,
        patterns
      });
      
      // 8. Risk management and position sizing
      const riskAdjustedSignal = this.applyRiskManagement(combinedSignal, volatilityAnalysis);
      
      return {
        signal: riskAdjustedSignal.confidence >= this.minConfidence ? riskAdjustedSignal : null,
        confidence: riskAdjustedSignal.confidence,
        analysis: this.generateAnalysis(combinedSignal, riskAdjustedSignal),
        indicators: {
          trend: trendAnalysis,
          momentum: momentumSignals,
          volatility: volatilityAnalysis,
          patterns
        }
      };
      
    } catch (error) {
      console.error('EURUSD Strategy analysis error:', error);
      return {
        signal: null,
        confidence: 0,
        analysis: 'Analysis failed due to data error',
        indicators: {}
      };
    }
  }

  private analyzeTrend(m5Closes: number[], h1Closes: number[], h4Closes: number[]) {
    // Multi-timeframe EMA analysis
    const m5_ema20 = TechnicalIndicators.ema(m5Closes, 20);
    const m5_ema50 = TechnicalIndicators.ema(m5Closes, 50);
    const h1_ema20 = TechnicalIndicators.ema(h1Closes, 20);
    const h4_ema20 = TechnicalIndicators.ema(h4Closes, 20);
    
    // KAMA for adaptive trend following
    const kama = TechnicalIndicators.kama(h1Closes, 14);
    
    const currentM5_20 = m5_ema20[m5_ema20.length - 1];
    const currentM5_50 = m5_ema50[m5_ema50.length - 1];
    const currentH1 = h1_ema20[h1_ema20.length - 1];
    const currentH4 = h4_ema20[h4_ema20.length - 1];
    const currentKAMA = kama[kama.length - 1];
    const currentPrice = m5Closes[m5Closes.length - 1];
    
    let trendScore = 0;
    let trendDirection = 'NEUTRAL';
    
    // M5 trend (weight: 30%)
    if (currentM5_20 > currentM5_50) trendScore += 30;
    else trendScore -= 30;
    
    // H1 trend alignment (weight: 40%)
    if (currentPrice > currentH1) trendScore += 40;
    else trendScore -= 40;
    
    // H4 trend alignment (weight: 30%)
    if (currentPrice > currentH4) trendScore += 30;
    else trendScore -= 30;
    
    // KAMA trend confirmation
    if (currentPrice > currentKAMA) trendScore += 20;
    else trendScore -= 20;
    
    if (trendScore > 50) trendDirection = 'BULLISH';
    else if (trendScore < -50) trendDirection = 'BEARISH';
    
    return {
      direction: trendDirection,
      score: Math.abs(trendScore),
      strength: Math.abs(trendScore) > 80 ? 'STRONG' : Math.abs(trendScore) > 40 ? 'MODERATE' : 'WEAK'
    };
  }

  private analyzeMomentum(m1Data: CandleData[], m5Data: CandleData[]) {
    const m1Closes = m1Data.map(c => c.close);
    const m5Closes = m5Data.map(c => c.close);
    const m1Highs = m1Data.map(c => c.high);
    const m1Lows = m1Data.map(c => c.low);
    
    // Advanced RSI with divergence
    const rsiData = TechnicalIndicators.rsi(m5Closes, 14);
    const currentRSI = rsiData.rsi[rsiData.rsi.length - 1];
    
    // MACD analysis
    const macd = TechnicalIndicators.macd(m5Closes, 12, 26, 9);
    const currentMACD = macd.macdLine[macd.macdLine.length - 1];
    const currentSignal = macd.signalLine[macd.signalLine.length - 1];
    const currentHist = macd.histogram[macd.histogram.length - 1];
    
    // Stochastic for overbought/oversold
    const stoch = TechnicalIndicators.stochastic(m1Highs, m1Lows, m1Closes, 14, 3);
    const currentStochK = stoch.kPercent[stoch.kPercent.length - 1];
    const currentStochD = stoch.dPercent[stoch.dPercent.length - 1];
    
    let momentumScore = 0;
    let signals: string[] = [];
    
    // RSI analysis
    if (currentRSI < 30) {
      momentumScore += 25;
      signals.push('RSI Oversold');
    } else if (currentRSI > 70) {
      momentumScore -= 25;
      signals.push('RSI Overbought');
    }
    
    // MACD analysis
    if (currentMACD > currentSignal && currentHist > 0) {
      momentumScore += 30;
      signals.push('MACD Bullish');
    } else if (currentMACD < currentSignal && currentHist < 0) {
      momentumScore -= 30;
      signals.push('MACD Bearish');
    }
    
    // Stochastic analysis
    if (currentStochK < 20 && currentStochD < 20) {
      momentumScore += 20;
      signals.push('Stoch Oversold');
    } else if (currentStochK > 80 && currentStochD > 80) {
      momentumScore -= 20;
      signals.push('Stoch Overbought');
    }
    
    return {
      score: momentumScore,
      rsi: currentRSI,
      macd: { line: currentMACD, signal: currentSignal, histogram: currentHist },
      stochastic: { k: currentStochK, d: currentStochD },
      signals
    };
  }

  private analyzeMarketMicrostructure(m1Data: CandleData[]) {
    // Order flow analysis using tick data approximation
    const volumes = m1Data.map(c => c.volume);
    const closes = m1Data.map(c => c.close);
    
    // Volume-weighted average price (VWAP) approximation
    let totalVolume = 0;
    let totalVolumePrice = 0;
    
    for (let i = Math.max(0, m1Data.length - 20); i < m1Data.length; i++) {
      const typical = (m1Data[i].high + m1Data[i].low + m1Data[i].close) / 3;
      totalVolume += m1Data[i].volume;
      totalVolumePrice += typical * m1Data[i].volume;
    }
    
    const vwap = totalVolumePrice / totalVolume;
    const currentPrice = closes[closes.length - 1];
    
    // Volume trend analysis
    const recentVolumes = volumes.slice(-10);
    const avgVolume = recentVolumes.reduce((a, b) => a + b, 0) / recentVolumes.length;
    const currentVolume = volumes[volumes.length - 1];
    
    const volumeRatio = currentVolume / avgVolume;
    
    return {
      vwap,
      priceVsVwap: currentPrice > vwap ? 'ABOVE' : 'BELOW',
      volumeRatio,
      volumeSignal: volumeRatio > 1.5 ? 'HIGH' : volumeRatio < 0.5 ? 'LOW' : 'NORMAL'
    };
  }

  private analyzeVolatility(m1Data: CandleData[], m5Data: CandleData[]) {
    const m1Highs = m1Data.map(c => c.high);
    const m1Lows = m1Data.map(c => c.low);
    const m1Closes = m1Data.map(c => c.close);
    const m5Closes = m5Data.map(c => c.close);
    
    // ATR for volatility measurement
    const atr = TechnicalIndicators.atr(m1Highs, m1Lows, m1Closes, 14);
    const currentATR = atr[atr.length - 1];
    
    // Bollinger Bands for volatility squeeze
    const bb = TechnicalIndicators.bollingerBands(m5Closes, 20, 2);
    const currentBB = bb[bb.length - 1];
    
    const currentPrice = m1Closes[m1Closes.length - 1];
    const bbPosition = (currentPrice - currentBB.lower) / (currentBB.upper - currentBB.lower);
    
    return {
      atr: currentATR,
      bbSqueeze: currentBB.squeeze,
      bbPosition,
      volatilityLevel: currentATR > 0.0015 ? 'HIGH' : currentATR < 0.0008 ? 'LOW' : 'NORMAL'
    };
  }

  private identifySupportResistance(h1Data: CandleData[], h4Data: CandleData[]) {
    const h1Highs = h1Data.map(c => c.high);
    const h1Lows = h1Data.map(c => c.low);
    const h4Highs = h4Data.map(c => c.high);
    const h4Lows = h4Data.map(c => c.low);
    
    // Find pivot points
    const h1Structure = TechnicalIndicators.detectMarketStructure(h1Highs, h1Lows, h1Data.map(c => c.close));
    const h4Structure = TechnicalIndicators.detectMarketStructure(h4Highs, h4Lows, h4Data.map(c => c.close));
    
    const supportLevels: number[] = [];
    const resistanceLevels: number[] = [];
    
    // H4 levels (stronger)
    h4Structure.swingLows.forEach(idx => {
      if (idx < h4Lows.length) supportLevels.push(h4Lows[idx]);
    });
    
    h4Structure.swingHighs.forEach(idx => {
      if (idx < h4Highs.length) resistanceLevels.push(h4Highs[idx]);
    });
    
    // H1 levels
    h1Structure.swingLows.forEach(idx => {
      if (idx < h1Lows.length) supportLevels.push(h1Lows[idx]);
    });
    
    h1Structure.swingHighs.forEach(idx => {
      if (idx < h1Highs.length) resistanceLevels.push(h1Highs[idx]);
    });
    
    return {
      support: supportLevels.sort((a, b) => b - a).slice(0, 3), // Top 3 closest support
      resistance: resistanceLevels.sort((a, b) => a - b).slice(0, 3) // Top 3 closest resistance
    };
  }

  private recognizePatterns(m5Data: CandleData[]) {
    // Simplified pattern recognition
    const patterns: string[] = [];
    
    if (m5Data.length < 10) return { patterns, score: 0 };
    
    const recent = m5Data.slice(-10);
    const closes = recent.map(c => c.close);
    const highs = recent.map(c => c.high);
    const lows = recent.map(c => c.low);
    
    // Double bottom/top detection
    const minPrice = Math.min(...lows);
    const maxPrice = Math.max(...highs);
    const minIndices = lows.map((price, idx) => price === minPrice ? idx : -1).filter(idx => idx !== -1);
    const maxIndices = highs.map((price, idx) => price === maxPrice ? idx : -1).filter(idx => idx !== -1);
    
    if (minIndices.length >= 2) patterns.push('Double Bottom');
    if (maxIndices.length >= 2) patterns.push('Double Top');
    
    // Trend continuation patterns
    const ema5 = TechnicalIndicators.ema(closes, 5);
    const ema10 = TechnicalIndicators.ema(closes, 10);
    
    if (ema5[ema5.length - 1] > ema10[ema10.length - 1]) {
      patterns.push('Bullish Continuation');
    } else {
      patterns.push('Bearish Continuation');
    }
    
    return {
      patterns,
      score: patterns.length * 10
    };
  }

  private combineSignals(signals: any): TradeSignal & { confidence: number } {
    let totalScore = 0;
    let direction: 'BUY' | 'SELL' = 'BUY';
    
    // Weight the signals
    const trendWeight = 0.35;
    const momentumWeight = 0.25;
    const microstructureWeight = 0.15;
    const volatilityWeight = 0.10;
    const patternWeight = 0.15;
    
    // Trend contribution
    if (signals.trend.direction === 'BULLISH') {
      totalScore += signals.trend.score * trendWeight;
    } else if (signals.trend.direction === 'BEARISH') {
      totalScore -= signals.trend.score * trendWeight;
    }
    
    // Momentum contribution
    totalScore += signals.momentum.score * momentumWeight;
    
    // Microstructure contribution
    if (signals.microstructure.priceVsVwap === 'ABOVE' && signals.microstructure.volumeRatio > 1.2) {
      totalScore += 20 * microstructureWeight;
    } else if (signals.microstructure.priceVsVwap === 'BELOW' && signals.microstructure.volumeRatio > 1.2) {
      totalScore -= 20 * microstructureWeight;
    }
    
    // Pattern contribution
    totalScore += signals.patterns.score * patternWeight;
    
    // Determine direction
    direction = totalScore > 0 ? 'BUY' : 'SELL';
    
    const confidence = Math.min(Math.abs(totalScore), 100);
    
    // Calculate entry, SL, TP based on current market conditions
    const currentPrice = 1.0850; // This should come from real market data
    const atr = signals.volatility.atr;
    
    let entryPrice = currentPrice;
    let stopLoss = direction === 'BUY' ? currentPrice - (atr * 2) : currentPrice + (atr * 2);
    let takeProfit = direction === 'BUY' ? currentPrice + (atr * 3) : currentPrice - (atr * 3);
    
    return {
      symbol: 'EURUSD',
      action: direction,
      confidence,
      entryPrice,
      stopLoss,
      takeProfit,
      volume: 0.1, // Will be adjusted by risk management
      timestamp: new Date(),
      strategy: this.name
    };
  }

  private applyRiskManagement(signal: TradeSignal & { confidence: number }, volatilityAnalysis: any): TradeSignal & { confidence: number } {
    // Adjust position size based on volatility and confidence
    let baseVolume = 0.1;
    
    // Reduce size in high volatility
    if (volatilityAnalysis.volatilityLevel === 'HIGH') {
      baseVolume *= 0.5;
    } else if (volatilityAnalysis.volatilityLevel === 'LOW') {
      baseVolume *= 1.5;
    }
    
    // Adjust based on confidence
    const confidenceMultiplier = signal.confidence / 100;
    baseVolume *= confidenceMultiplier;
    
    // Maximum position size limit
    baseVolume = Math.min(baseVolume, 0.5);
    baseVolume = Math.max(baseVolume, 0.01);
    
    return {
      ...signal,
      volume: Math.round(baseVolume * 100) / 100
    };
  }

  private generateAnalysis(combinedSignal: any, finalSignal: any): string {
    return `EURUSD AI Analysis: ${finalSignal.action} signal with ${finalSignal.confidence}% confidence. 
    Entry: ${finalSignal.entryPrice}, SL: ${finalSignal.stopLoss}, TP: ${finalSignal.takeProfit}. 
    Volume: ${finalSignal.volume} lots. Strategy combines multi-timeframe trend analysis, 
    advanced momentum indicators, and market microstructure analysis.`;
  }
}