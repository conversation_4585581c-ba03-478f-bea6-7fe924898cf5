const MetaApi = require('metaapi.cloud-sdk').default;

async function checkAccountType() {
  console.log('🔍 Checking MetaAPI Account Configuration...\n');

  const token = process.env.VITE_METAAPI_TOKEN;
  const accountId = process.env.VITE_MASTER_ACCOUNT_ID || '7716b187-9109-4115-bff2-2846272b507a';

  if (!token) {
    console.log('❌ VITE_METAAPI_TOKEN not found in environment');
    return;
  }

  try {
    const api = new MetaApi(token);
    const account = await api.metatraderAccountApi.getAccount(accountId);

    console.log('📋 Account Information:');
    console.log(`   Account ID: ${account.id}`);
    console.log(`   Name: ${account.name}`);
    console.log(`   Server: ${account.server}`);
    console.log(`   Login: ${account.login}`);
    console.log(`   Type: ${account.type}`);
    console.log(`   State: ${account.state}`);
    console.log(`   Connection Status: ${account.connectionStatus}`);
    console.log(`   Magic: ${account.magic || 'Not set'}`);
    console.log(`   Quoting: ${account.quoteStreamingIntervalInSeconds}s intervals`);

    // Check if it's demo or live
    const isDemo = account.server.toLowerCase().includes('demo') || 
                   account.type === 'demo' ||
                   account.name.toLowerCase().includes('demo');

    console.log(`\n🎯 Account Type Analysis:`);
    console.log(`   Is Demo Account: ${isDemo ? 'YES' : 'NO'}`);
    console.log(`   Server Type: ${account.server.includes('Demo') ? 'DEMO' : 'LIVE'}`);

    if (isDemo) {
      console.log(`\n⚠️  IMPORTANT: This is a DEMO account!`);
      console.log(`   - All trades will be simulated`);
      console.log(`   - No real money will be used`);
      console.log(`   - Perfect for testing strategies`);
      console.log(`\n💡 To switch to live trading:`);
      console.log(`   1. Create a live account in MetaAPI dashboard`);
      console.log(`   2. Update VITE_MASTER_ACCOUNT_ID in .env file`);
      console.log(`   3. Ensure the server name doesn't contain 'Demo'`);
    } else {
      console.log(`\n✅ This appears to be a LIVE account!`);
      console.log(`   - Trades will use real money`);
      console.log(`   - Be cautious with position sizes`);
      console.log(`   - Monitor risk management carefully`);
    }

    // Check if account is ready for trading
    console.log(`\n🔧 Trading Readiness:`);
    console.log(`   Deployed: ${account.state === 'DEPLOYED' ? 'YES' : 'NO'}`);
    console.log(`   Connected: ${account.connectionStatus === 'CONNECTED' ? 'YES' : 'NO'}`);
    
    const canTrade = account.state === 'DEPLOYED' && account.connectionStatus === 'CONNECTED';
    console.log(`   Ready to Trade: ${canTrade ? 'YES' : 'NO'}`);

    if (!canTrade) {
      console.log(`\n❌ Account not ready for trading:`);
      if (account.state !== 'DEPLOYED') {
        console.log(`   - Account state is '${account.state}', needs to be 'DEPLOYED'`);
      }
      if (account.connectionStatus !== 'CONNECTED') {
        console.log(`   - Connection status is '${account.connectionStatus}', needs to be 'CONNECTED'`);
      }
    }

  } catch (error) {
    console.error('❌ Failed to check account:', error.message);
    
    if (error.message.includes('not found')) {
      console.log('\n💡 Possible solutions:');
      console.log('   - Check if VITE_MASTER_ACCOUNT_ID is correct');
      console.log('   - Verify account exists in MetaAPI dashboard');
      console.log('   - Ensure MetaAPI token has proper permissions');
    }
  }
}

checkAccountType().catch(console.error);
