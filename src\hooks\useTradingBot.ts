import { useState, useEffect, useCallback } from 'react';
import { TradingBot, BotConfig, BotStatus, BotPerformance } from '../ai/TradingBot';
import { AccountInfo, MarketData } from '../services/MetaApiService';

export const useTradingBot = () => {
  const [bot, setBot] = useState<TradingBot | null>(null);
  const [botStatus, setBotStatus] = useState<BotStatus>({
    isRunning: false,
    lastSignalTime: null,
    tradesExecutedToday: 0,
    openPositions: 0,
    totalProfit: 0,
    winRate: 0
  });
  const [botPerformance, setBotPerformance] = useState<BotPerformance>({
    totalTrades: 0,
    winningTrades: 0,
    losingTrades: 0,
    totalProfit: 0,
    totalLoss: 0,
    winRate: 0,
    profitFactor: 0,
    maxDrawdown: 0,
    averageWin: 0,
    averageLoss: 0
  });
  const [accountInfo, setAccountInfo] = useState<AccountInfo | null>(null);
  const [marketData, setMarketData] = useState<{ [symbol: string]: MarketData }>({});
  const [isInitializing, setIsInitializing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const defaultConfig: BotConfig = {
    enabled: true,  // ✅ ENABLE BOT BY DEFAULT
    symbols: ['EURUSD', 'XAUUSD'],
    riskLevel: 'balanced',
    maxTradesPerDay: 10,
    maxOpenPositions: 3,
    accountId: import.meta.env.VITE_MASTER_ACCOUNT_ID || '168683'
  };

  const initializeBot = useCallback(async (config: BotConfig = defaultConfig) => {
    try {
      setIsInitializing(true);
      setError(null);

      const newBot = new TradingBot(config);
      await newBot.initialize();
      
      setBot(newBot);
      setBotStatus(newBot.getStatus());
      setBotPerformance(newBot.getPerformance());

      // Get initial account info
      const accountData = await newBot.getAccountInfo();
      setAccountInfo(accountData);

      // Get initial market data
      const marketDataPromises = config.symbols.map(async (symbol) => {
        const data = await newBot.getCurrentMarketData(symbol);
        return { symbol, data };
      });

      const marketResults = await Promise.all(marketDataPromises);
      const marketDataMap: { [symbol: string]: MarketData } = {};
      marketResults.forEach(({ symbol, data }) => {
        marketDataMap[symbol] = data;
      });
      setMarketData(marketDataMap);

      console.log('Trading bot initialized successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize bot';
      setError(errorMessage);
      console.error('Failed to initialize bot:', err);
    } finally {
      setIsInitializing(false);
    }
  }, []);

  const startBot = useCallback(async () => {
    if (!bot) {
      setError('Bot not initialized');
      return;
    }

    try {
      await bot.start();
      setBotStatus(bot.getStatus());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start bot';
      setError(errorMessage);
    }
  }, [bot]);

  const stopBot = useCallback(async () => {
    if (!bot) return;

    try {
      await bot.stop();
      setBotStatus(bot.getStatus());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to stop bot';
      setError(errorMessage);
    }
  }, [bot]);

  const updateBotConfig = useCallback((newConfig: Partial<BotConfig>) => {
    if (!bot) return;

    bot.updateConfig(newConfig);
    setBotStatus(bot.getStatus());
  }, [bot]);

  const refreshData = useCallback(async () => {
    if (!bot) return;

    try {
      setBotStatus(bot.getStatus());
      setBotPerformance(bot.getPerformance());
      
      const accountData = await bot.getAccountInfo();
      setAccountInfo(accountData);

      // ✅ Update market data with live updates
      const liveMarketData = await bot.getLiveMarketDataWithUpdates();
      setMarketData(liveMarketData);
    } catch (err) {
      console.error('Failed to refresh data:', err);
    }
  }, [bot]);

  // ✅ Auto-refresh data every 15 seconds for more dynamic updates
  useEffect(() => {
    if (!bot) return;

    // Initial data load
    refreshData();

    // Refresh every 15 seconds for live data (even when not running to show market data)
    const interval = setInterval(refreshData, 15000);
    return () => clearInterval(interval);
  }, [bot, refreshData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (bot) {
        bot.disconnect();
      }
    };
  }, [bot]);

  return {
    // Bot instance and status
    bot,
    botStatus,
    botPerformance,
    accountInfo,
    marketData,
    
    // Loading and error states
    isInitializing,
    error,
    
    // Actions
    initializeBot,
    startBot,
    stopBot,
    updateBotConfig,
    refreshData,
    
    // Computed values
    isConnected: !!bot && !!accountInfo,
    isRunning: botStatus.isRunning,
    
    // Helper methods
    getTradeHistory: () => bot?.getTradeHistory() || [],
    clearError: () => setError(null)
  };
};