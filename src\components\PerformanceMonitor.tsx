import React, { useState, useEffect } from 'react';
import { Activity, Zap, Database, Clock, AlertTriangle, CheckCircle } from 'lucide-react';
import { getMetaApiService } from '../services/MetaApiService';

interface PerformanceStats {
  connectionStats: any;
  lastUpdate: Date;
  apiCallsPerMinute: number;
  cacheHitRate: number;
  averageResponseTime: number;
}

const PerformanceMonitor: React.FC = () => {
  const [stats, setStats] = useState<PerformanceStats | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [apiCallCount, setApiCallCount] = useState(0);
  const [cacheHits, setCacheHits] = useState(0);
  const [totalRequests, setTotalRequests] = useState(0);

  useEffect(() => {
    const updateStats = () => {
      try {
        const metaApiService = getMetaApiService();
        const connectionStats = metaApiService.getConnectionStats();
        
        setStats({
          connectionStats,
          lastUpdate: new Date(),
          apiCallsPerMinute: apiCallCount,
          cacheHitRate: totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0,
          averageResponseTime: 150 // Placeholder - would need actual timing
        });
      } catch (error) {
        console.error('Failed to get performance stats:', error);
      }
    };

    updateStats();
    const interval = setInterval(updateStats, 5000);

    return () => clearInterval(interval);
  }, [apiCallCount, cacheHits, totalRequests]);

  // Reset counters every minute
  useEffect(() => {
    const resetInterval = setInterval(() => {
      setApiCallCount(0);
      setCacheHits(0);
      setTotalRequests(0);
    }, 60000);

    return () => clearInterval(resetInterval);
  }, []);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-colors z-50"
        title="Show Performance Monitor"
      >
        <Activity className="w-5 h-5" />
      </button>
    );
  }

  if (!stats) {
    return (
      <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 w-80 z-50">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold text-gray-900 dark:text-white">Performance Monitor</h3>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ×
          </button>
        </div>
        <div className="text-center text-gray-500">Loading...</div>
      </div>
    );
  }

  const getStatusColor = (connected: boolean) => {
    return connected ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = (connected: boolean) => {
    return connected ? <CheckCircle className="w-4 h-4" /> : <AlertTriangle className="w-4 h-4" />;
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 w-80 z-50 border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-gray-900 dark:text-white flex items-center">
          <Activity className="w-4 h-4 mr-2" />
          Performance Monitor
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          ×
        </button>
      </div>

      <div className="space-y-3 text-sm">
        {/* Connection Status */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600 dark:text-gray-400 flex items-center">
            {getStatusIcon(stats.connectionStats.connected)}
            <span className="ml-2">Connection</span>
          </span>
          <span className={getStatusColor(stats.connectionStats.connected)}>
            {stats.connectionStats.connected ? 'Connected' : 'Disconnected'}
          </span>
        </div>

        {/* Service Ready */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600 dark:text-gray-400 flex items-center">
            <Zap className="w-4 h-4" />
            <span className="ml-2">Service</span>
          </span>
          <span className={getStatusColor(stats.connectionStats.serviceReady)}>
            {stats.connectionStats.serviceReady ? 'Ready' : 'Not Ready'}
          </span>
        </div>

        {/* Cache Stats */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600 dark:text-gray-400 flex items-center">
            <Database className="w-4 h-4" />
            <span className="ml-2">Cache Hit Rate</span>
          </span>
          <span className="text-blue-600 font-medium">
            {stats.cacheHitRate.toFixed(1)}%
          </span>
        </div>

        {/* API Calls */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600 dark:text-gray-400 flex items-center">
            <Clock className="w-4 h-4" />
            <span className="ml-2">API Calls/min</span>
          </span>
          <span className="text-purple-600 font-medium">
            {stats.apiCallsPerMinute}
          </span>
        </div>

        {/* Queue Status */}
        <div className="flex items-center justify-between">
          <span className="text-gray-600 dark:text-gray-400">Queue Length</span>
          <span className={stats.connectionStats.cacheStats.queueLength > 5 ? 'text-orange-600' : 'text-green-600'}>
            {stats.connectionStats.cacheStats.queueLength}
          </span>
        </div>

        {/* Cache Details */}
        <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-2">
          <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
            <div className="flex justify-between">
              <span>Price Cache:</span>
              <span>{stats.connectionStats.cacheStats.priceCache} symbols</span>
            </div>
            <div className="flex justify-between">
              <span>Account Cached:</span>
              <span>{stats.connectionStats.cacheStats.accountInfoCached ? 'Yes' : 'No'}</span>
            </div>
            <div className="flex justify-between">
              <span>Positions Cached:</span>
              <span>{stats.connectionStats.cacheStats.positionsCached ? 'Yes' : 'No'}</span>
            </div>
            <div className="flex justify-between">
              <span>Historical Cache:</span>
              <span>{stats.connectionStats.cacheStats.historicalDataCache} items</span>
            </div>
          </div>
        </div>

        {/* Last Update */}
        <div className="text-xs text-gray-400 text-center border-t border-gray-200 dark:border-gray-600 pt-2">
          Last updated: {stats.lastUpdate.toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

export default PerformanceMonitor;
