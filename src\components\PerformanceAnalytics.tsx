import React from 'react';
import { TrendingUp, TrendingDown, Target, Award, Calendar, DollarSign } from 'lucide-react';

const PerformanceAnalytics: React.FC = () => {
  const performanceData = [
    { label: 'Win Rate', value: '78.5%', icon: Award, color: 'text-green-600', bg: 'bg-green-100 dark:bg-green-900' },
    { label: 'Average Profit', value: '$127.45', icon: TrendingUp, color: 'text-blue-600', bg: 'bg-blue-100 dark:bg-blue-900' },
    { label: 'Max Drawdown', value: '4.2%', icon: TrendingDown, color: 'text-red-600', bg: 'bg-red-100 dark:bg-red-900' },
    { label: 'Monthly ROI', value: '15.7%', icon: Target, color: 'text-purple-600', bg: 'bg-purple-100 dark:bg-purple-900' },
    { label: 'Total Trades', value: '342', icon: Calendar, color: 'text-orange-600', bg: 'bg-orange-100 dark:bg-orange-900' },
    { label: 'Total Profit', value: '$12,847', icon: DollarSign, color: 'text-green-600', bg: 'bg-green-100 dark:bg-green-900' }
  ];

  const monthlyData = [
    { month: 'Jan', profit: 850, trades: 23 },
    { month: 'Feb', profit: 1200, trades: 31 },
    { month: 'Mar', profit: 980, trades: 28 },
    { month: 'Apr', profit: 1450, trades: 35 },
    { month: 'May', profit: 1680, trades: 42 },
    { month: 'Jun', profit: 2100, trades: 48 }
  ];

  const topPerformingPairs = [
    { pair: 'EURUSD', trades: 89, winRate: 82, profit: '+$3,247' },
    { pair: 'XAUUSD', trades: 67, winRate: 75, profit: '+$2,891' },
    { pair: 'GBPJPY', trades: 54, winRate: 71, profit: '+$1,956' },
    { pair: 'USDJPY', trades: 43, winRate: 79, profit: '+$1,543' }
  ];

  return (
    <div className="space-y-6">
      {/* Key Performance Metrics */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Performance Analytics</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {performanceData.map((metric, index) => {
            const Icon = metric.icon;
            return (
              <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{metric.label}</p>
                    <p className={`text-2xl font-bold ${metric.color}`}>{metric.value}</p>
                  </div>
                  <div className={`p-3 rounded-lg ${metric.bg}`}>
                    <Icon className={`w-6 h-6 ${metric.color}`} />
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Monthly Performance Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Monthly Performance</h3>
        <div className="space-y-4">
          {monthlyData.map((data, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 text-sm font-medium text-gray-600 dark:text-gray-400">
                  {data.month}
                </div>
                <div className="flex-1">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                    <div 
                      className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500"
                      style={{ width: `${(data.profit / 2500) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-4 text-sm">
                <div className="text-right">
                  <p className="font-semibold text-green-600">${data.profit}</p>
                  <p className="text-gray-600 dark:text-gray-400">{data.trades} trades</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Top Performing Pairs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Top Performing Currency Pairs</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">Pair</th>
                <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">Trades</th>
                <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">Win Rate</th>
                <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">Profit</th>
                <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">Performance</th>
              </tr>
            </thead>
            <tbody>
              {topPerformingPairs.map((pair, index) => (
                <tr key={index} className="border-b border-gray-100 dark:border-gray-700">
                  <td className="py-3 px-2">
                    <span className="font-semibold text-gray-900 dark:text-white">{pair.pair}</span>
                  </td>
                  <td className="py-3 px-2 text-gray-600 dark:text-gray-400">{pair.trades}</td>
                  <td className="py-3 px-2">
                    <span className="text-green-600 font-medium">{pair.winRate}%</span>
                  </td>
                  <td className="py-3 px-2">
                    <span className="text-green-600 font-semibold">{pair.profit}</span>
                  </td>
                  <td className="py-3 px-2">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${pair.winRate}%` }}
                      ></div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Recent Performance Insights */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Insights</h3>
        <div className="space-y-4">
          <div className="flex items-start space-x-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <TrendingUp className="w-5 h-5 text-green-600 mt-0.5" />
            <div>
              <p className="font-medium text-green-800 dark:text-green-200">Strong Performance Trend</p>
              <p className="text-sm text-green-700 dark:text-green-300">Your trading performance has improved 23% over the last 30 days, with consistent profits across major pairs.</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <Target className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <p className="font-medium text-blue-800 dark:text-blue-200">Risk Management Excellence</p>
              <p className="text-sm text-blue-700 dark:text-blue-300">Your maximum drawdown of 4.2% is well within safe limits, showing excellent risk control.</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <Award className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <p className="font-medium text-yellow-800 dark:text-yellow-200">Top Trader Ranking</p>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">You're in the top 15% of traders on the platform based on consistent returns and risk management.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceAnalytics;