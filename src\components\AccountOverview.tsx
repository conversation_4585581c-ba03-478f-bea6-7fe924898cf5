import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, DollarSign, Shield, Globe, Wifi, WifiOff, AlertCircle, Settings } from 'lucide-react';
import { MetaApiService } from '../services/MetaApiService';

const AccountOverview: React.FC = () => {
  const [accountData, setAccountData] = useState<any>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const [error, setError] = useState<string | null>(null);
  const [metaApi] = useState(() => new MetaApiService());

  useEffect(() => {
    const connectToMetaApi = async () => {
      try {
        // Check if MetaAPI service is ready before attempting connection
        if (!metaApi.isServiceReady()) {
          const initError = metaApi.getInitializationError();
          throw new Error(initError || 'MetaAPI service not ready');
        }

        setConnectionStatus('connecting');
        setError(null);
        
        const accountId = import.meta.env.VITE_MASTER_ACCOUNT_ID;
        
        // Check if account ID is configured
        if (!accountId || accountId === 'YOUR_ACTUAL_METAAPI_ACCOUNT_ID') {
          throw new Error('MetaAPI account ID not configured. Please update VITE_MASTER_ACCOUNT_ID in your .env file with your actual account login number.');
        }
        
        console.log(`Connecting to MetaAPI account login ${accountId}...`);
        
        // Connect to the configured account
        await metaApi.connect(accountId);
        
        const accountInfo = await metaApi.getAccountInfo();
        setAccountData(accountInfo);
        setConnectionStatus('connected');
        
        console.log('Successfully connected to MetaAPI account:', accountInfo);
        
        // Set up periodic updates
        const interval = setInterval(async () => {
          try {
            if (metaApi.isConnectionHealthy()) {
              const updatedInfo = await metaApi.getAccountInfo();
              setAccountData(updatedInfo);
            }
          } catch (err) {
            console.error('Failed to update account info:', err);
          }
        }, 10000); // Update every 10 seconds

        return () => clearInterval(interval);
        
      } catch (err) {
        console.error('Failed to connect to MetaAPI:', err);
        const errorMessage = err instanceof Error ? err.message : 'Connection failed';
        setError(errorMessage);
        setConnectionStatus('disconnected');
      }
    };

    connectToMetaApi();

    return () => {
      metaApi.disconnect();
    };
  }, [metaApi]);

  if (connectionStatus === 'connecting') {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-center space-x-3">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <div className="text-center">
            <p className="text-lg text-gray-600 dark:text-gray-400">Connecting to MetaAPI...</p>
            <p className="text-sm text-gray-500">Account: {import.meta.env.VITE_MASTER_ACCOUNT_ID}</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    const isConfigurationError = error.includes('not configured') || 
                                error.includes('YOUR_ACTUAL_METAAPI_ACCOUNT_ID') ||
                                error.includes('YOUR_METAAPI_TOKEN_HERE') ||
                                error.includes('token not configured');
    
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="flex items-start space-x-3 text-red-600">
          <AlertCircle className="w-6 h-6 mt-1" />
          <div className="flex-1">
            <h3 className="font-semibold">
              {isConfigurationError ? 'Configuration Required' : 'Connection Error'}
            </h3>
            <p className="text-sm mt-1">{error}</p>
            <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <p className="text-xs text-red-700 dark:text-red-300">
                <strong>Setup Instructions:</strong>
              </p>
              <ul className="text-xs text-red-600 dark:text-red-400 mt-1 space-y-1">
                <li>• Create a MetaAPI account at metaapi.cloud if you haven't already</li>
                <li>• Get your MetaAPI token from the dashboard</li>
                <li>• Add your MT4/MT5 trading account to MetaAPI</li>
                <li>• Deploy and connect your account</li>
                <li>• Update your .env file with:</li>
                <li className="ml-4">- VITE_METAAPI_TOKEN=your_actual_token</li>
                <li className="ml-4">- VITE_MASTER_ACCOUNT_ID=your_account_login_number</li>
                <li>• Restart the development server</li>
              </ul>
            </div>
            {isConfigurationError && (
              <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Settings className="w-4 h-4 text-blue-600" />
                  <span className="text-xs text-blue-800 dark:text-blue-200">
                    Current token: {import.meta.env.VITE_METAAPI_TOKEN ? 'Set' : 'Not set'} | 
                    Account login: {import.meta.env.VITE_MASTER_ACCOUNT_ID || 'Not set'}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (!accountData) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <p className="text-gray-600 dark:text-gray-400">Loading account data...</p>
      </div>
    );
  }

  const balanceChange = accountData.equity - accountData.balance;
  const balanceChangePercent = accountData.balance > 0 ? (balanceChange / accountData.balance) * 100 : 0;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Trading Account</h2>
        <div className="flex items-center space-x-2">
          <Wifi className="w-5 h-5 text-green-500" />
          <span className="text-sm text-green-600 font-medium">LIVE DATA</span>
          <span className="text-xs text-gray-500">#{accountData.accountNumber}</span>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Account Balance */}
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm">Account Balance</p>
              <p className="text-2xl font-bold">{accountData.currency} {accountData.balance.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</p>
              <p className={`text-blue-200 text-sm flex items-center mt-1 ${
                balanceChange >= 0 ? 'text-green-200' : 'text-red-200'
              }`}>
                {balanceChange >= 0 ? <TrendingUp className="w-4 h-4 mr-1" /> : <TrendingDown className="w-4 h-4 mr-1" />}
                {accountData.currency} {Math.abs(balanceChange).toFixed(2)} ({balanceChangePercent.toFixed(2)}%)
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-blue-200" />
          </div>
        </div>

        {/* Equity */}
        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm">Equity</p>
              <p className="text-2xl font-bold">{accountData.currency} {accountData.equity.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</p>
              <p className="text-green-200 text-sm">
                Floating P&L: {accountData.currency} {(accountData.equity - accountData.balance).toFixed(2)}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-200" />
          </div>
        </div>

        {/* Free Margin */}
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm">Free Margin</p>
              <p className="text-2xl font-bold">{accountData.currency} {accountData.freeMargin.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</p>
              <p className="text-purple-200 text-sm">Available for trading</p>
            </div>
            <Shield className="w-8 h-8 text-purple-200" />
          </div>
        </div>

        {/* Margin Level */}
        <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-4 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-100 text-sm">Margin Level</p>
              <p className="text-2xl font-bold">{accountData.marginLevel.toFixed(2)}%</p>
              <p className="text-orange-200 text-sm">
                {accountData.marginLevel > 200 ? 'Healthy margin' : 
                 accountData.marginLevel > 100 ? 'Moderate risk' : 'High risk'}
              </p>
            </div>
            <Globe className="w-8 h-8 text-orange-200" />
          </div>
        </div>
      </div>

      {/* Account Details */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">Account Number</p>
          <p className="font-semibold text-gray-900 dark:text-white">{accountData.accountNumber}</p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">Broker</p>
          <p className="font-semibold text-gray-900 dark:text-white">{accountData.broker}</p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">Server Status</p>
          <div className="flex items-center">
            <div className={`w-2 h-2 rounded-full mr-2 ${
              accountData.connected ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            <p className={`font-semibold ${
              accountData.connected ? 'text-green-600' : 'text-red-600'
            }`}>
              {accountData.connected ? 'Connected' : 'Disconnected'}
            </p>
          </div>
        </div>
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">Leverage</p>
          <p className="font-semibold text-gray-900 dark:text-white">1:{accountData.leverage}</p>
        </div>
      </div>

      {/* Connection Info */}
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <div className="flex items-center space-x-2">
          <Wifi className="w-4 h-4 text-blue-600" />
          <span className="text-sm text-blue-800 dark:text-blue-200">
            Connected to {accountData.server} • Real-time data streaming
          </span>
        </div>
      </div>
    </div>
  );
};

export default AccountOverview;