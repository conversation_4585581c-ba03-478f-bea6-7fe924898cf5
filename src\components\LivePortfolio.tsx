import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Clock, Target, RefreshCw } from 'lucide-react';
import { MetaApiService, Position } from '../services/MetaApiService';

const LivePortfolio: React.FC = () => {
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [metaApi] = useState(() => new MetaApiService());
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  const fetchPositions = async () => {
    try {
      setError(null);
      const openPositions = await metaApi.getOpenPositions();
      setPositions(openPositions);
      setLastUpdate(new Date());
    } catch (err) {
      console.error('Failed to fetch positions:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch positions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const connectAndFetch = async () => {
      try {
        if (!metaApi.isConnectionHealthy()) {
          const accountId = import.meta.env.VITE_MASTER_ACCOUNT_ID || '168683';
          await metaApi.connect(accountId);
        }
        await fetchPositions();
      } catch (err) {
        console.error('Failed to connect and fetch positions:', err);
        setError(err instanceof Error ? err.message : 'Connection failed');
        setLoading(false);
      }
    };

    connectAndFetch();

    // Set up periodic updates
    const interval = setInterval(fetchPositions, 10000); // Update every 10 seconds

    return () => {
      clearInterval(interval);
      metaApi.disconnect();
    };
  }, [metaApi]);

  const totalFloatingPnL = positions.reduce((sum, pos) => sum + pos.profit, 0);
  const totalSwap = positions.reduce((sum, pos) => sum + pos.swap, 0);
  const totalCommission = positions.reduce((sum, pos) => sum + pos.commission, 0);
  const winningPositions = positions.filter(pos => pos.profit > 0).length;
  const losingPositions = positions.filter(pos => pos.profit < 0).length;

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-center space-x-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading live positions...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchPositions}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry Connection
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Live Portfolio Summary */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Live Portfolio Summary</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={fetchPositions}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
            {lastUpdate && (
              <span className="text-xs text-gray-500">
                Updated: {lastUpdate.toLocaleTimeString()}
              </span>
            )}
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Floating P&L</p>
            <p className={`text-2xl font-bold ${
              totalFloatingPnL >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              ${totalFloatingPnL.toFixed(2)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Open Positions</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">{positions.length}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Winning/Losing</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              <span className="text-green-600">{winningPositions}</span>/
              <span className="text-red-600">{losingPositions}</span>
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Total Fees</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              ${(totalSwap + totalCommission).toFixed(2)}
            </p>
          </div>
        </div>
      </div>

      {/* Open Positions */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Live Open Positions ({positions.length})
        </h3>
        
        {positions.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-600 dark:text-gray-400">No open positions</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">Symbol</th>
                  <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">Type</th>
                  <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">Volume</th>
                  <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">Entry</th>
                  <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">Current</th>
                  <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">SL/TP</th>
                  <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">P&L</th>
                  <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">Time</th>
                </tr>
              </thead>
              <tbody>
                {positions.map((position) => (
                  <tr key={position.id} className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="py-3 px-2">
                      <span className="font-semibold text-gray-900 dark:text-white">{position.symbol}</span>
                    </td>
                    <td className="py-3 px-2">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        position.type === 'BUY' 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      }`}>
                        {position.type}
                      </span>
                    </td>
                    <td className="py-3 px-2 text-gray-600 dark:text-gray-400">{position.volume}</td>
                    <td className="py-3 px-2 text-gray-900 dark:text-white">{position.openPrice.toFixed(5)}</td>
                    <td className="py-3 px-2 text-gray-900 dark:text-white">{position.currentPrice.toFixed(5)}</td>
                    <td className="py-3 px-2 text-gray-600 dark:text-gray-400 text-sm">
                      <div>
                        {position.stopLoss ? position.stopLoss.toFixed(5) : 'None'}/
                        {position.takeProfit ? position.takeProfit.toFixed(5) : 'None'}
                      </div>
                    </td>
                    <td className={`py-3 px-2 font-semibold ${
                      position.profit >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      ${position.profit.toFixed(2)}
                    </td>
                    <td className="py-3 px-2 text-gray-600 dark:text-gray-400 text-sm flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {position.openTime.toLocaleTimeString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Position Details */}
      {positions.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Position Analysis</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Swap</p>
              <p className={`text-lg font-semibold ${
                totalSwap >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                ${totalSwap.toFixed(2)}
              </p>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Commission</p>
              <p className="text-lg font-semibold text-red-600">${Math.abs(totalCommission).toFixed(2)}</p>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">Net P&L</p>
              <p className={`text-lg font-semibold ${
                (totalFloatingPnL + totalSwap + totalCommission) >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                ${(totalFloatingPnL + totalSwap + totalCommission).toFixed(2)}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LivePortfolio;