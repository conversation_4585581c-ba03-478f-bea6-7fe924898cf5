import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Clock,
  Target,
  RefreshCw,
  DollarSign,
  Activity,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  X,
  Calendar,
  Timer,
  Percent
} from 'lucide-react';
import { getMetaApiService, Position } from '../services/MetaApiService';

interface PortfolioStats {
  totalFloatingPnL: number;
  totalSwap: number;
  totalCommission: number;
  netPnL: number;
  winningPositions: number;
  losingPositions: number;
  winRate: number;
  averageWin: number;
  averageLoss: number;
  largestWin: number;
  largestLoss: number;
  totalVolume: number;
}

const LivePortfolio: React.FC = () => {
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [selectedPosition, setSelectedPosition] = useState<Position | null>(null);
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const [sortBy, setSortBy] = useState<'profit' | 'time' | 'symbol'>('profit');
  const [filterBy, setFilterBy] = useState<'all' | 'winning' | 'losing'>('all');

  const fetchPositions = async () => {
    try {
      setError(null);
      const metaApiService = getMetaApiService();
      const openPositions = await metaApiService.getOpenPositions();
      setPositions(openPositions);
      setLastUpdate(new Date());
    } catch (err) {
      console.error('Failed to fetch positions:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch positions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const connectAndFetch = async () => {
      try {
        const metaApiService = getMetaApiService();
        if (!metaApiService.isConnectionHealthy()) {
          const accountId = import.meta.env.VITE_MASTER_ACCOUNT_ID || '168683';
          await metaApiService.connect(accountId);
        }
        await fetchPositions();
      } catch (err) {
        console.error('Failed to connect and fetch positions:', err);
        setError(err instanceof Error ? err.message : 'Connection failed');
        setLoading(false);
      }
    };

    connectAndFetch();

    // Set up periodic updates
    const interval = setInterval(fetchPositions, 10000); // Update every 10 seconds

    return () => {
      clearInterval(interval);
      // Note: Don't disconnect singleton service as other components may be using it
    };
  }, []);

  // Calculate comprehensive portfolio statistics
  const calculatePortfolioStats = (): PortfolioStats => {
    const totalFloatingPnL = positions.reduce((sum, pos) => sum + pos.profit, 0);
    const totalSwap = positions.reduce((sum, pos) => sum + pos.swap, 0);
    const totalCommission = positions.reduce((sum, pos) => sum + pos.commission, 0);
    const netPnL = totalFloatingPnL + totalSwap + totalCommission;

    const winningPositions = positions.filter(pos => pos.profit > 0);
    const losingPositions = positions.filter(pos => pos.profit < 0);
    const winRate = positions.length > 0 ? (winningPositions.length / positions.length) * 100 : 0;

    const averageWin = winningPositions.length > 0
      ? winningPositions.reduce((sum, pos) => sum + pos.profit, 0) / winningPositions.length
      : 0;
    const averageLoss = losingPositions.length > 0
      ? losingPositions.reduce((sum, pos) => sum + pos.profit, 0) / losingPositions.length
      : 0;

    const largestWin = winningPositions.length > 0
      ? Math.max(...winningPositions.map(pos => pos.profit))
      : 0;
    const largestLoss = losingPositions.length > 0
      ? Math.min(...losingPositions.map(pos => pos.profit))
      : 0;

    const totalVolume = positions.reduce((sum, pos) => sum + pos.volume, 0);

    return {
      totalFloatingPnL,
      totalSwap,
      totalCommission,
      netPnL,
      winningPositions: winningPositions.length,
      losingPositions: losingPositions.length,
      winRate,
      averageWin,
      averageLoss,
      largestWin,
      largestLoss,
      totalVolume
    };
  };

  const stats = calculatePortfolioStats();

  // Filter and sort positions
  const getFilteredPositions = () => {
    let filtered = positions;

    switch (filterBy) {
      case 'winning':
        filtered = positions.filter(pos => pos.profit > 0);
        break;
      case 'losing':
        filtered = positions.filter(pos => pos.profit < 0);
        break;
      default:
        filtered = positions;
    }

    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'profit':
          return b.profit - a.profit;
        case 'time':
          return new Date(b.openTime).getTime() - new Date(a.openTime).getTime();
        case 'symbol':
          return a.symbol.localeCompare(b.symbol);
        default:
          return 0;
      }
    });
  };

  const filteredPositions = getFilteredPositions();

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-center space-x-3 py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <div className="text-center">
              <p className="text-lg text-gray-600 dark:text-gray-400">Loading Portfolio...</p>
              <p className="text-sm text-gray-500 mt-1">Fetching live positions and analytics</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <div className="text-center py-12">
            <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Portfolio Connection Error</h3>
            <p className="text-red-600 mb-6 max-w-md mx-auto">{error}</p>
            <div className="space-x-3">
              <button
                onClick={fetchPositions}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                <RefreshCw className="w-4 h-4 inline mr-2" />
                Retry Connection
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Portfolio Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold">Live Portfolio</h2>
            <p className="text-blue-100">Real-time position monitoring & analytics</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={fetchPositions}
              className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
              title="Refresh Portfolio"
            >
              <RefreshCw className="w-5 h-5" />
            </button>
            {lastUpdate && (
              <div className="text-right">
                <p className="text-xs text-blue-100">Last Updated</p>
                <p className="text-sm font-medium">{lastUpdate.toLocaleTimeString()}</p>
              </div>
            )}
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
            <div className="flex items-center space-x-2 mb-2">
              <DollarSign className="w-5 h-5 text-green-300" />
              <p className="text-sm text-blue-100">Net P&L</p>
            </div>
            <p className={`text-2xl font-bold ${
              stats.netPnL >= 0 ? 'text-green-300' : 'text-red-300'
            }`}>
              ${stats.netPnL.toFixed(2)}
            </p>
            <p className="text-xs text-blue-200 mt-1">
              {stats.netPnL >= 0 ? '+' : ''}{((stats.netPnL / 1000) * 100).toFixed(2)}%
            </p>
          </div>

          <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
            <div className="flex items-center space-x-2 mb-2">
              <Activity className="w-5 h-5 text-blue-300" />
              <p className="text-sm text-blue-100">Positions</p>
            </div>
            <p className="text-2xl font-bold text-white">{positions.length}</p>
            <p className="text-xs text-blue-200 mt-1">
              {stats.totalVolume.toFixed(2)} lots total
            </p>
          </div>

          <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
            <div className="flex items-center space-x-2 mb-2">
              <Percent className="w-5 h-5 text-yellow-300" />
              <p className="text-sm text-blue-100">Win Rate</p>
            </div>
            <p className="text-2xl font-bold text-white">{stats.winRate.toFixed(1)}%</p>
            <p className="text-xs text-blue-200 mt-1">
              <span className="text-green-300">{stats.winningPositions}W</span> /
              <span className="text-red-300">{stats.losingPositions}L</span>
            </p>
          </div>

          <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
            <div className="flex items-center space-x-2 mb-2">
              <BarChart3 className="w-5 h-5 text-purple-300" />
              <p className="text-sm text-blue-100">Risk/Reward</p>
            </div>
            <p className="text-2xl font-bold text-white">
              {stats.averageLoss !== 0 ? (Math.abs(stats.averageWin / stats.averageLoss)).toFixed(2) : '0.00'}
            </p>
            <p className="text-xs text-blue-200 mt-1">
              Avg: ${stats.averageWin.toFixed(2)} / ${Math.abs(stats.averageLoss).toFixed(2)}
            </p>
          </div>
        </div>
      </div>

      {/* Enhanced Positions Section */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Open Positions ({filteredPositions.length})
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Monitor and manage your active trades
            </p>
          </div>

          <div className="flex items-center space-x-3 mt-4 sm:mt-0">
            {/* Filter Options */}
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value as 'all' | 'winning' | 'losing')}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            >
              <option value="all">All Positions</option>
              <option value="winning">Winning Only</option>
              <option value="losing">Losing Only</option>
            </select>

            {/* Sort Options */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'profit' | 'time' | 'symbol')}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            >
              <option value="profit">Sort by P&L</option>
              <option value="time">Sort by Time</option>
              <option value="symbol">Sort by Symbol</option>
            </select>

            {/* View Toggle */}
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  viewMode === 'table'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400'
                }`}
              >
                Table
              </button>
              <button
                onClick={() => setViewMode('cards')}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  viewMode === 'cards'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400'
                }`}
              >
                Cards
              </button>
            </div>
          </div>
        </div>

        {filteredPositions.length === 0 && (
          <div className="text-center py-12">
            <Activity className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {positions.length === 0 ? 'No Open Positions' : 'No Matching Positions'}
            </h4>
            <p className="text-gray-600 dark:text-gray-400">
              {positions.length === 0
                ? 'Your trading bot will show active positions here once trades are executed.'
                : 'Try adjusting your filter criteria to see more positions.'}
            </p>
          </div>
        )}

        {filteredPositions.length > 0 && viewMode === 'table' && (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-4 px-3 text-sm font-medium text-gray-600 dark:text-gray-400">Position</th>
                  <th className="text-left py-4 px-3 text-sm font-medium text-gray-600 dark:text-gray-400">Entry/Current</th>
                  <th className="text-left py-4 px-3 text-sm font-medium text-gray-600 dark:text-gray-400">Risk Management</th>
                  <th className="text-left py-4 px-3 text-sm font-medium text-gray-600 dark:text-gray-400">Performance</th>
                  <th className="text-left py-4 px-3 text-sm font-medium text-gray-600 dark:text-gray-400">Duration</th>
                  <th className="text-left py-4 px-3 text-sm font-medium text-gray-600 dark:text-gray-400">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredPositions.map((position) => (
                  <tr key={position.id} className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <td className="py-4 px-3">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          position.profit >= 0 ? 'bg-green-500' : 'bg-red-500'
                        }`}></div>
                        <div>
                          <p className="font-semibold text-gray-900 dark:text-white">{position.symbol}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <span className={`px-2 py-1 rounded text-xs font-medium ${
                              position.type === 'BUY'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            }`}>
                              {position.type}
                            </span>
                            <span className="text-xs text-gray-500">{position.volume} lots</span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-3">
                      <div>
                        <p className="text-sm text-gray-900 dark:text-white">
                          Entry: <span className="font-mono">{position.openPrice.toFixed(5)}</span>
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          Current: <span className="font-mono">{position.currentPrice.toFixed(5)}</span>
                        </p>
                        <p className={`text-xs mt-1 ${
                          position.type === 'BUY'
                            ? position.currentPrice > position.openPrice ? 'text-green-600' : 'text-red-600'
                            : position.currentPrice < position.openPrice ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {position.type === 'BUY'
                            ? (position.currentPrice - position.openPrice > 0 ? '+' : '') + (position.currentPrice - position.openPrice).toFixed(5)
                            : (position.openPrice - position.currentPrice > 0 ? '+' : '') + (position.openPrice - position.currentPrice).toFixed(5)
                          } pips
                        </p>
                      </div>
                    </td>
                    <td className="py-4 px-3">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <Target className="w-3 h-3 text-red-500" />
                          <span className="text-xs text-gray-600 dark:text-gray-400">
                            SL: {position.stopLoss ? position.stopLoss.toFixed(5) : 'None'}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Target className="w-3 h-3 text-green-500" />
                          <span className="text-xs text-gray-600 dark:text-gray-400">
                            TP: {position.takeProfit ? position.takeProfit.toFixed(5) : 'None'}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-3">
                      <div>
                        <p className={`text-lg font-bold ${
                          position.profit >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          ${position.profit.toFixed(2)}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Swap: ${position.swap.toFixed(2)} | Comm: ${position.commission.toFixed(2)}
                        </p>
                      </div>
                    </td>
                    <td className="py-4 px-3">
                      <div className="flex items-center space-x-1">
                        <Timer className="w-3 h-3 text-gray-400" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {new Date(position.openTime).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {new Date(position.openTime).toLocaleTimeString()}
                      </p>
                    </td>
                    <td className="py-4 px-3">
                      <button
                        onClick={() => setSelectedPosition(position)}
                        className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {filteredPositions.length > 0 && viewMode === 'cards' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredPositions.map((position) => (
              <div key={position.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${
                      position.profit >= 0 ? 'bg-green-500' : 'bg-red-500'
                    }`}></div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">{position.symbol}</h4>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    position.type === 'BUY'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}>
                    {position.type}
                  </span>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Volume:</span>
                    <span className="text-gray-900 dark:text-white">{position.volume} lots</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Entry:</span>
                    <span className="text-gray-900 dark:text-white font-mono">{position.openPrice.toFixed(5)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Current:</span>
                    <span className="text-gray-900 dark:text-white font-mono">{position.currentPrice.toFixed(5)}</span>
                  </div>
                </div>

                <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-xs text-gray-500">P&L</p>
                      <p className={`text-lg font-bold ${
                        position.profit >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        ${position.profit.toFixed(2)}
                      </p>
                    </div>
                    <button
                      onClick={() => setSelectedPosition(position)}
                      className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                      title="View Details"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Enhanced Analytics */}
      {positions.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Portfolio Analytics</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="w-5 h-5 text-green-600" />
                <p className="text-sm text-green-700 dark:text-green-300">Best Performer</p>
              </div>
              <p className="text-2xl font-bold text-green-800 dark:text-green-200">
                ${stats.largestWin.toFixed(2)}
              </p>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">Largest Win</p>
            </div>

            <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingDown className="w-5 h-5 text-red-600" />
                <p className="text-sm text-red-700 dark:text-red-300">Worst Performer</p>
              </div>
              <p className="text-2xl font-bold text-red-800 dark:text-red-200">
                ${stats.largestLoss.toFixed(2)}
              </p>
              <p className="text-xs text-red-600 dark:text-red-400 mt-1">Largest Loss</p>
            </div>

            <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <DollarSign className="w-5 h-5 text-blue-600" />
                <p className="text-sm text-blue-700 dark:text-blue-300">Total Fees</p>
              </div>
              <p className="text-2xl font-bold text-blue-800 dark:text-blue-200">
                ${(Math.abs(stats.totalSwap) + Math.abs(stats.totalCommission)).toFixed(2)}
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                Swap: ${stats.totalSwap.toFixed(2)} | Comm: ${Math.abs(stats.totalCommission).toFixed(2)}
              </p>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <BarChart3 className="w-5 h-5 text-purple-600" />
                <p className="text-sm text-purple-700 dark:text-purple-300">Exposure</p>
              </div>
              <p className="text-2xl font-bold text-purple-800 dark:text-purple-200">
                {stats.totalVolume.toFixed(2)}
              </p>
              <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">Total Lots</p>
            </div>
          </div>

          {/* Symbol Distribution */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Position Distribution</h4>
            <div className="space-y-2">
              {Object.entries(
                positions.reduce((acc, pos) => {
                  acc[pos.symbol] = (acc[pos.symbol] || 0) + 1;
                  return acc;
                }, {} as Record<string, number>)
              ).map(([symbol, count]) => (
                <div key={symbol} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">{symbol}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(count / positions.length) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-900 dark:text-white w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Position Detail Modal */}
      {selectedPosition && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Position Details - {selectedPosition.symbol}
              </h3>
              <button
                onClick={() => setSelectedPosition(null)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="p-6 space-y-6">
              {/* Position Overview */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Position Type</p>
                  <div className="flex items-center space-x-2">
                    <span className={`px-3 py-1 rounded text-sm font-medium ${
                      selectedPosition.type === 'BUY'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {selectedPosition.type}
                    </span>
                    <span className="text-gray-600 dark:text-gray-400">{selectedPosition.volume} lots</span>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Current P&L</p>
                  <p className={`text-2xl font-bold ${
                    selectedPosition.profit >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    ${selectedPosition.profit.toFixed(2)}
                  </p>
                </div>
              </div>

              {/* Price Information */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white">Price Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <p className="text-sm text-blue-600 dark:text-blue-400 mb-1">Entry Price</p>
                    <p className="text-xl font-mono font-bold text-blue-800 dark:text-blue-200">
                      {selectedPosition.openPrice.toFixed(5)}
                    </p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Current Price</p>
                    <p className="text-xl font-mono font-bold text-gray-900 dark:text-white">
                      {selectedPosition.currentPrice.toFixed(5)}
                    </p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <p className="text-sm text-purple-600 dark:text-purple-400 mb-1">Price Change</p>
                    <p className={`text-xl font-mono font-bold ${
                      selectedPosition.type === 'BUY'
                        ? selectedPosition.currentPrice > selectedPosition.openPrice ? 'text-green-600' : 'text-red-600'
                        : selectedPosition.currentPrice < selectedPosition.openPrice ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {selectedPosition.type === 'BUY'
                        ? (selectedPosition.currentPrice - selectedPosition.openPrice > 0 ? '+' : '') + (selectedPosition.currentPrice - selectedPosition.openPrice).toFixed(5)
                        : (selectedPosition.openPrice - selectedPosition.currentPrice > 0 ? '+' : '') + (selectedPosition.openPrice - selectedPosition.currentPrice).toFixed(5)
                      }
                    </p>
                  </div>
                </div>
              </div>

              {/* Risk Management */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white">Risk Management</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <Target className="w-5 h-5 text-red-600" />
                      <p className="text-sm text-red-700 dark:text-red-300">Stop Loss</p>
                    </div>
                    <p className="text-lg font-mono font-bold text-red-800 dark:text-red-200">
                      {selectedPosition.stopLoss ? selectedPosition.stopLoss.toFixed(5) : 'Not Set'}
                    </p>
                    {selectedPosition.stopLoss && (
                      <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                        Risk: {Math.abs(selectedPosition.openPrice - selectedPosition.stopLoss).toFixed(5)} pips
                      </p>
                    )}
                  </div>

                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <Target className="w-5 h-5 text-green-600" />
                      <p className="text-sm text-green-700 dark:text-green-300">Take Profit</p>
                    </div>
                    <p className="text-lg font-mono font-bold text-green-800 dark:text-green-200">
                      {selectedPosition.takeProfit ? selectedPosition.takeProfit.toFixed(5) : 'Not Set'}
                    </p>
                    {selectedPosition.takeProfit && (
                      <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                        Target: {Math.abs(selectedPosition.takeProfit - selectedPosition.openPrice).toFixed(5)} pips
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Additional Details */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white">Additional Details</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400">Swap</p>
                    <p className={`text-lg font-bold ${
                      selectedPosition.swap >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      ${selectedPosition.swap.toFixed(2)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400">Commission</p>
                    <p className="text-lg font-bold text-red-600">
                      ${Math.abs(selectedPosition.commission).toFixed(2)}
                    </p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400">Open Time</p>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {new Date(selectedPosition.openTime).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>

              {selectedPosition.comment && (
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p className="text-sm text-blue-600 dark:text-blue-400 mb-1">Trade Comment</p>
                  <p className="text-gray-900 dark:text-white">{selectedPosition.comment}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LivePortfolio;