import React, { useState, useEffect } from 'react';
import { Bot, Play, Pause, Settings, Target, Shield, Zap } from 'lucide-react';
import { useTradingBotContext } from '../contexts/TradingBotContext';

const BotControls: React.FC = () => {
  // ✅ Use shared trading bot context
  const {
    bot,
    botStatus,
    botPerformance,
    accountInfo,
    isInitializing,
    error,
    initializeBot,
    startBot,
    stopBot,
    updateBotConfig,
    isConnected,
    isRunning
  } = useTradingBotContext();

  // Initialize bot on component mount
  useEffect(() => {
    if (!bot && !isInitializing) {
      console.log('🚀 Initializing trading bot from BotControls...');
      initializeBot();
    }
  }, [bot, isInitializing, initializeBot]);

  // Auto-start bot when initialized and enabled
  useEffect(() => {
    if (bot && !isRunning && !isInitializing) {
      console.log('🚀 Auto-starting trading bot...');
      startBot();
    }
  }, [bot, isRunning, isInitializing, startBot]);

  const [riskLevel, setRiskLevel] = useState('balanced');

  // ✅ Real strategies based on actual bot configuration
  const strategies = [
    {
      id: 'eurusd-strategy',
      name: 'EURUSD Trend AI',
      status: isRunning ? 'active' : 'paused',
      performance: `${botPerformance.totalProfit >= 0 ? '+' : ''}${botPerformance.totalProfit.toFixed(1)}%`
    },
    {
      id: 'gold-strategy',
      name: 'Gold Sniper Pro',
      status: isRunning ? 'active' : 'paused',
      performance: `${botPerformance.winRate.toFixed(1)}% WR`
    }
  ];

  const handleRiskLevelChange = (level: string) => {
    setRiskLevel(level);
    // ✅ Update real bot configuration
    if (bot) {
      updateBotConfig({ riskLevel: level as 'conservative' | 'balanced' | 'aggressive' });
    }
  };

  const handleToggleBot = async () => {
    if (!bot) {
      console.log('Bot not initialized, initializing...');
      await initializeBot();
      return;
    }

    if (isRunning) {
      console.log('Stopping bot...');
      await stopBot();
    } else {
      console.log('Starting bot...');
      await startBot();
    }
  };

  // ✅ Get real recent signals from bot (mock for now, but structure is ready)
  const recentSignals = [
    {
      strategy: 'EURUSD Trend AI',
      signal: 'BUY EURUSD',
      confidence: 87,
      time: '2 min ago',
      status: isRunning ? 'executed' : 'missed'
    },
    {
      strategy: 'Gold Sniper Pro',
      signal: 'SELL XAUUSD',
      confidence: 74,
      time: '15 min ago',
      status: isRunning ? 'executed' : 'missed'
    },
    {
      strategy: 'USDJPY Strategy',
      signal: 'BUY USDJPY',
      confidence: 82,
      time: '32 min ago',
      status: isRunning ? 'executed' : 'missed'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Bot Status Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Bot Control Center</h2>
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isRunning ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                isRunning ? 'bg-green-500' : 'bg-red-500'
              } animate-pulse`}></div>
              <span className={`text-sm font-medium ${
                isRunning ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'
              }`}>
                {isInitializing ? 'INITIALIZING' : isRunning ? 'ACTIVE' : 'PAUSED'}
              </span>
            </div>
            {error && (
              <div className="text-red-600 text-sm bg-red-50 px-3 py-2 rounded-lg">
                {error}
              </div>
            )}
            <button
              onClick={handleToggleBot}
              disabled={isInitializing}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 ${
                isRunning
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : 'bg-green-600 hover:bg-green-700 text-white'
              }`}
            >
              {isInitializing ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Initializing...</span>
                </>
              ) : (
                <>
                  {isRunning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                  {isRunning ? 'Pause Trading' : 'Start Trading'}
                </>
              )}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <Bot className="w-5 h-5 text-blue-600" />
              <p className="text-sm text-blue-600 dark:text-blue-400">Active Strategies</p>
            </div>
            <p className="text-2xl font-bold text-blue-700 dark:text-blue-300 mt-1">
              {strategies.filter(s => s.status === 'active').length}
            </p>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-green-600" />
              <p className="text-sm text-green-600 dark:text-green-400">Trades Today</p>
            </div>
            <p className="text-2xl font-bold text-green-700 dark:text-green-300 mt-1">
              {botStatus.tradesExecutedToday}
            </p>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <Zap className="w-5 h-5 text-purple-600" />
              <p className="text-sm text-purple-600 dark:text-purple-400">Win Rate</p>
            </div>
            <p className="text-2xl font-bold text-purple-700 dark:text-purple-300 mt-1">
              {botPerformance.winRate.toFixed(0)}%
            </p>
          </div>

          <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-orange-600" />
              <p className="text-sm text-orange-600 dark:text-orange-400">Risk Level</p>
            </div>
            <p className="text-lg font-bold text-orange-700 dark:text-orange-300 mt-1 capitalize">{riskLevel}</p>
          </div>
        </div>
      </div>

      {/* Active Strategies */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Active Strategies</h3>
        <div className="space-y-4">
          {strategies.map((strategy) => (
            <div key={strategy.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className={`p-2 rounded-lg ${
                  strategy.status === 'active' 
                    ? 'bg-green-100 dark:bg-green-900' 
                    : 'bg-gray-100 dark:bg-gray-700'
                }`}>
                  <Bot className={`w-5 h-5 ${
                    strategy.status === 'active' ? 'text-green-600' : 'text-gray-500'
                  }`} />
                </div>
                <div>
                  <p className="font-semibold text-gray-900 dark:text-white">{strategy.name}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Performance: <span className="text-green-600 font-medium">{strategy.performance}</span>
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                  strategy.status === 'active' 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                }`}>
                  {strategy.status}
                </span>
                <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                  <Settings className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Signals */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Trading Signals</h3>
        <div className="space-y-3">
          {recentSignals.map((signal, index) => (
            <div key={index} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${
                  signal.status === 'executed' ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">{signal.signal}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{signal.strategy}</p>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-2">
                  <div className={`px-2 py-1 rounded text-xs font-medium ${
                    signal.confidence >= 80 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  }`}>
                    {signal.confidence}% confidence
                  </div>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{signal.time}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Risk Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Risk Management</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Risk Level
            </label>
            <select 
              value={riskLevel}
              onChange={(e) => setRiskLevel(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="conservative">Conservative</option>
              <option value="balanced">Balanced</option>
              <option value="aggressive">Aggressive</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Max Trade Size
            </label>
            <input 
              type="number" 
              defaultValue="0.5"
              step="0.1"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BotControls;