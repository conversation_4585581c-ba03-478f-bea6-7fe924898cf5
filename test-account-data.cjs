// Test Account and Historical Data Access
// Run this with: node test-account-data.cjs

const MetaApi = require('metaapi.cloud-sdk').default;
require('dotenv').config();

async function testAccountData() {
  console.log('🧪 Testing Account and Historical Data Access...\n');

  const token = process.env.VITE_METAAPI_TOKEN;
  const accountId = process.env.VITE_MASTER_ACCOUNT_ID;
  
  if (!token || !accountId) {
    console.error('❌ Missing environment variables');
    console.log('Required: VITE_METAAPI_TOKEN, VITE_MASTER_ACCOUNT_ID');
    return false;
  }

  console.log(`🔑 Token: ${token.substring(0, 50)}...`);
  console.log(`🏦 Account ID: ${accountId}`);

  try {
    console.log('\n1️⃣ Initializing MetaAPI...');
    const api = new MetaApi(token);
    
    console.log('2️⃣ Getting account...');
    let account;
    try {
      account = await api.metatraderAccountApi.getAccount(accountId);
      console.log('✅ Account found!');
      console.log(`   Login: ${account.login}`);
      console.log(`   Name: ${account.name || 'N/A'}`);
      console.log(`   Server: ${account.server}`);
      console.log(`   State: ${account.state}`);
      console.log(`   Platform: ${account.platform}`);
      console.log(`   Type: ${account.type}`);
    } catch (error) {
      console.log(`❌ Account access failed: ${error.message}`);
      
      // Try to list accounts if direct access fails
      console.log('\n🔍 Trying to list all accounts...');
      try {
        const accountsApi = api.metatraderAccountApi;
        const accounts = await accountsApi.getAccounts();
        console.log(`Found ${accounts.length} accounts:`);
        accounts.forEach(acc => {
          console.log(`  - ${acc.id}: ${acc.login} (${acc.state})`);
        });
      } catch (listError) {
        console.log(`❌ Cannot list accounts: ${listError.message}`);
      }
      return false;
    }

    if (account.state !== 'DEPLOYED') {
      console.log(`❌ Account state is ${account.state}, expected DEPLOYED`);
      console.log('💡 Account needs to be deployed to access historical data');
      return false;
    }

    console.log('\n3️⃣ Testing connection...');
    const connection = account.getRPCConnection();
    
    try {
      console.log('🔌 Connecting...');
      await connection.connect();
      console.log('✅ Connected');
      
      console.log('⏳ Waiting for synchronization...');
      await connection.waitSynchronized({ timeoutInSeconds: 30 });
      console.log('✅ Synchronized');
      
      console.log('\n4️⃣ Testing historical data...');
      
      // Test 1: Recent EURUSD data
      console.log('📊 Testing EURUSD 1h data (10 candles)...');
      try {
        const eurusdCandles = await connection.getCandles('EURUSD', '1h', 10);
        if (eurusdCandles && eurusdCandles.length > 0) {
          console.log(`✅ EURUSD: ${eurusdCandles.length} candles`);
          console.log(`   Latest: ${eurusdCandles[eurusdCandles.length - 1].time} - ${eurusdCandles[eurusdCandles.length - 1].close}`);
        } else {
          console.log('❌ EURUSD: No data');
        }
      } catch (error) {
        console.log(`❌ EURUSD failed: ${error.message}`);
      }
      
      // Test 2: Gold data
      console.log('📊 Testing XAUUSD 1h data (10 candles)...');
      try {
        const goldCandles = await connection.getCandles('XAUUSD', '1h', 10);
        if (goldCandles && goldCandles.length > 0) {
          console.log(`✅ XAUUSD: ${goldCandles.length} candles`);
          console.log(`   Latest: ${goldCandles[goldCandles.length - 1].time} - ${goldCandles[goldCandles.length - 1].close}`);
        } else {
          console.log('❌ XAUUSD: No data');
        }
      } catch (error) {
        console.log(`❌ XAUUSD failed: ${error.message}`);
      }
      
      // Test 3: Historical data with start date
      console.log('📊 Testing historical data with start date...');
      try {
        const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
        console.log(`   Requesting from: ${startDate.toISOString()}`);
        
        const historicalCandles = await connection.getCandles('EURUSD', '1h', 50, startDate);
        if (historicalCandles && historicalCandles.length > 0) {
          console.log(`✅ Historical: ${historicalCandles.length} candles`);
          console.log(`   From: ${historicalCandles[0].time}`);
          console.log(`   To: ${historicalCandles[historicalCandles.length - 1].time}`);
        } else {
          console.log('❌ Historical: No data');
        }
      } catch (error) {
        console.log(`❌ Historical failed: ${error.message}`);
      }
      
      await connection.close();
      console.log('✅ Connection closed');
      
      return true;
      
    } catch (error) {
      console.log(`❌ Connection failed: ${error.message}`);
      try {
        await connection.close();
      } catch (closeError) {
        // Ignore close errors
      }
      return false;
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

// Run the test
testAccountData()
  .then(success => {
    console.log('\n📊 Final Result:');
    console.log('='.repeat(50));
    
    if (success) {
      console.log('🎉 SUCCESS: Real historical data is available!');
      console.log('\n✅ Your backtest will use REAL market data:');
      console.log('• Accurate price movements and volatility');
      console.log('• Real market gaps and weekend breaks');
      console.log('• Authentic spread and slippage simulation');
      console.log('• Reliable strategy performance validation');
      console.log('\n🚀 Recommendations:');
      console.log('• Use longer backtesting periods for better insights');
      console.log('• Test multiple timeframes and symbols');
      console.log('• Compare backtest results with live performance');
      console.log('• Validate strategies before live trading');
    } else {
      console.log('❌ FALLBACK: Using mock data for backtesting');
      console.log('\n🔄 Mock data features:');
      console.log('• Realistic price movements with proper volatility');
      console.log('• Appropriate base prices for each symbol');
      console.log('• Consistent data for testing purposes');
      console.log('• Good for initial strategy development');
      console.log('\n💡 To get real data:');
      console.log('• Ensure account is properly deployed');
      console.log('• Check MetaAPI subscription limits');
      console.log('• Contact broker about historical data access');
      console.log('• Consider upgrading MetaAPI plan if needed');
    }
    
    process.exit(0);
  })
  .catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
