import React from 'react';

const AppStatus: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 max-w-md w-full mx-4">
        <div className="text-center">
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            BOTFOREX Loading...
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            React app is initializing
          </p>
          <div className="space-y-2 text-sm text-left">
            <div className="flex justify-between">
              <span className="text-gray-500">React:</span>
              <span className="text-green-600">✅ Loaded</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">TypeScript:</span>
              <span className="text-green-600">✅ Compiled</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Vite HMR:</span>
              <span className="text-green-600">✅ Active</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">MetaAPI:</span>
              <span className="text-yellow-600">⏳ Initializing</span>
            </div>
          </div>
          <div className="mt-6 text-xs text-gray-500">
            If this screen persists, check browser console for errors
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppStatus;
