<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bot Debug Console</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #1a1a1a; color: #00ff00; }
        .log { margin: 5px 0; padding: 5px; background: #2a2a2a; border-radius: 3px; }
        .error { color: #ff4444; }
        .success { color: #44ff44; }
        .warning { color: #ffaa44; }
        .info { color: #4444ff; }
        #logs { max-height: 500px; overflow-y: auto; border: 1px solid #444; padding: 10px; }
        button { padding: 10px; margin: 5px; background: #333; color: #fff; border: none; cursor: pointer; }
        button:hover { background: #555; }
    </style>
</head>
<body>
    <h1>🤖 BOTFOREX Debug Console</h1>
    
    <div>
        <button onclick="testEnvironment()">Test Environment</button>
        <button onclick="testMetaAPI()">Test MetaAPI</button>
        <button onclick="testBotInit()">Test Bot Init</button>
        <button onclick="testExistingBot()">Test Existing Bot</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>
    
    <div id="logs"></div>

    <script type="module">
        // Import MetaAPI
        import MetaApi from 'metaapi.cloud-sdk';
        
        window.MetaApi = MetaApi;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `[${timestamp}] ${message}`;
            document.getElementById('logs').appendChild(logDiv);
            document.getElementById('logs').scrollTop = document.getElementById('logs').scrollHeight;
            console.log(message);
        }
        
        window.log = log;
        
        window.testEnvironment = function() {
            log('🔍 Testing Environment Variables...', 'info');
            
            // Check if we can access environment variables
            const token = import.meta.env.VITE_METAAPI_TOKEN;
            const accountId = import.meta.env.VITE_MASTER_ACCOUNT_ID;
            
            log(`Token: ${token ? 'Present ✅' : 'Missing ❌'}`, token ? 'success' : 'error');
            log(`Account ID: ${accountId || 'Missing ❌'}`, accountId ? 'success' : 'error');
            
            if (!token || !accountId) {
                log('❌ Environment variables missing - bot cannot work', 'error');
                return;
            }
            
            log('✅ Environment variables OK', 'success');
        };
        
        window.testMetaAPI = async function() {
            log('🔍 Testing MetaAPI Connection...', 'info');
            
            const token = import.meta.env.VITE_METAAPI_TOKEN;
            const accountId = import.meta.env.VITE_MASTER_ACCOUNT_ID;
            
            if (!token || !accountId) {
                log('❌ Missing environment variables', 'error');
                return;
            }
            
            try {
                const api = new MetaApi(token);
                log('✅ MetaAPI instance created', 'success');
                
                const account = await api.metatraderAccountApi.getAccount(accountId);
                log(`✅ Account found: ${account.name}`, 'success');
                log(`   State: ${account.state}`, 'info');
                log(`   Connection: ${account.connectionStatus}`, 'info');
                log(`   Server: ${account.server}`, 'info');
                
                const isReady = account.state === 'DEPLOYED' && account.connectionStatus === 'CONNECTED';
                log(`   Ready for trading: ${isReady ? '✅ YES' : '❌ NO'}`, isReady ? 'success' : 'error');
                
                if (isReady) {
                    log('🎯 MetaAPI connection successful!', 'success');
                } else {
                    log('❌ Account not ready for trading', 'error');
                }
                
            } catch (error) {
                log(`❌ MetaAPI test failed: ${error.message}`, 'error');
            }
        };
        
        window.testBotInit = async function() {
            log('🔍 Testing Bot Initialization...', 'info');

            try {
                // Try to import and create the trading bot
                const { TradingBot } = await import('./src/ai/TradingBot.ts');
                log('✅ TradingBot class imported', 'success');

                const config = {
                    enabled: true,
                    symbols: ['EURUSD', 'XAUUSD'],
                    riskLevel: 'balanced',
                    maxTradesPerDay: 10,
                    maxOpenPositions: 3,
                    accountId: import.meta.env.VITE_MASTER_ACCOUNT_ID
                };

                log('Creating bot instance...', 'info');
                const bot = new TradingBot(config);
                log('✅ Bot instance created', 'success');

                log('Initializing bot...', 'info');
                await bot.initialize();
                log('✅ Bot initialized successfully', 'success');

                log('Starting bot...', 'info');
                await bot.start();
                log('✅ Bot started successfully', 'success');

                log('🎯 Bot is now running!', 'success');

                // Test bot status
                const status = bot.getStatus();
                log(`Bot Status: Running=${status.isRunning}, Trades=${status.tradesExecutedToday}, Positions=${status.openPositions}`, 'info');

                // Test manual trading cycle
                log('Testing manual trading cycle...', 'info');
                if (bot.manualTradingCycle) {
                    await bot.manualTradingCycle();
                    log('✅ Manual trading cycle completed', 'success');
                } else {
                    log('❌ Manual trading cycle method not available', 'error');
                }

                // Store bot globally for debugging
                window.debugBot = bot;

            } catch (error) {
                log(`❌ Bot initialization failed: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
            }
        };
        
        window.testExistingBot = async function() {
            log('🔍 Testing Existing Bot from Main App...', 'info');

            try {
                // Try to access the bot from the main application context
                if (window.parent && window.parent !== window) {
                    log('Checking parent window for bot...', 'info');
                    // We're in an iframe, try to access parent
                    const parentBot = window.parent.debugBot;
                    if (parentBot) {
                        log('✅ Found bot in parent window', 'success');
                        const status = parentBot.getStatus();
                        log(`Bot Status: Running=${status.isRunning}, Trades=${status.tradesExecutedToday}`, 'info');
                        return;
                    }
                }

                // Check if there's a global bot instance
                if (window.debugBot) {
                    log('✅ Found existing bot instance', 'success');
                    const status = window.debugBot.getStatus();
                    log(`Bot Status: Running=${status.isRunning}, Trades=${status.tradesExecutedToday}`, 'info');

                    // Test manual cycle
                    if (window.debugBot.manualTradingCycle) {
                        log('Testing manual cycle on existing bot...', 'info');
                        await window.debugBot.manualTradingCycle();
                        log('✅ Manual cycle completed', 'success');
                    }
                    return;
                }

                // Try to import the trading bot context
                const { useTradingBotContext } = await import('./src/contexts/TradingBotContext.tsx');
                log('✅ TradingBotContext imported', 'success');

                log('❌ No existing bot found - main app may not be running', 'warning');
                log('💡 Try opening the main BOTFOREX app first', 'info');

            } catch (error) {
                log(`❌ Failed to test existing bot: ${error.message}`, 'error');
            }
        };

        window.clearLogs = function() {
            document.getElementById('logs').innerHTML = '';
        };

        // Auto-run environment test on load
        setTimeout(() => {
            log('🚀 Debug console loaded', 'success');
            testEnvironment();
        }, 1000);
    </script>
</body>
</html>
