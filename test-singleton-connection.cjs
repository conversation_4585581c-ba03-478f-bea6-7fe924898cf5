const MetaApi = require('metaapi.cloud-sdk').default;
require('dotenv').config();

async function testSingletonConnection() {
  console.log('🧪 Testing Singleton Connection Pattern...\n');
  
  const token = process.env.VITE_METAAPI_TOKEN;
  const accountId = process.env.VITE_MASTER_ACCOUNT_ID;
  
  if (!token || !accountId) {
    console.error('❌ Missing environment variables');
    return;
  }
  
  try {
    // Simulate multiple components trying to connect simultaneously
    console.log('📡 Simulating multiple simultaneous connection attempts...');
    
    const api = new MetaApi(token);
    const accounts = await api.metatraderAccountApi.getAccountsWithInfiniteScrollPagination();
    const account = accounts.find(acc => acc.login === accountId);
    
    if (!account) {
      console.error(`❌ Account ${accountId} not found`);
      return;
    }
    
    console.log(`✅ Account found: ${account.name} (${account.login})`);
    console.log(`📊 Account state: ${account.state}`);
    
    // Test single connection with proper cleanup
    console.log('\n🔗 Testing single connection...');
    const connection = account.getStreamingConnection();
    
    const startTime = Date.now();
    await connection.connect();
    const connectTime = Date.now() - startTime;
    
    console.log(`✅ Connected in ${connectTime}ms`);
    
    // Test synchronization with timeout
    console.log('⏳ Testing synchronization with timeout...');
    const syncStart = Date.now();
    
    try {
      await Promise.race([
        connection.waitSynchronized(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Sync timeout')), 15000)
        )
      ]);
      const syncTime = Date.now() - syncStart;
      console.log(`✅ Synchronized in ${syncTime}ms`);
    } catch (error) {
      console.log(`⚠️  Synchronization timeout after 15s, continuing...`);
    }
    
    // Test basic functionality
    console.log('\n📈 Testing basic functionality...');
    try {
      const accountInfo = await connection.getAccountInformation();
      console.log(`✅ Account balance: $${accountInfo.balance}`);
      console.log(`✅ Account equity: $${accountInfo.equity}`);
    } catch (error) {
      console.log(`⚠️  Account info failed: ${error.message}`);
    }
    
    // Clean disconnect
    console.log('\n🔌 Disconnecting...');
    await connection.close();
    console.log('✅ Disconnected cleanly');
    
    console.log('\n🎉 Singleton connection test completed successfully!');
    console.log('\n💡 Recommendations:');
    console.log('   - Use singleton pattern to prevent multiple connections');
    console.log('   - Implement connection pooling');
    console.log('   - Add proper timeout handling');
    console.log('   - Handle rate limiting gracefully');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.status === 429) {
      console.log('\n🚨 RATE LIMITING DETECTED!');
      console.log('   - You have exceeded your MetaAPI subscription quota');
      console.log('   - Current limit: 25 subscriptions');
      console.log('   - Recommended actions:');
      console.log('     1. Use singleton pattern (implemented)');
      console.log('     2. Reduce connection attempts');
      console.log('     3. Wait for quota reset');
      console.log('     4. Consider upgrading your MetaAPI plan');
      
      if (error.metadata?.recommendedRetryTime) {
        const retryTime = new Date(error.metadata.recommendedRetryTime);
        console.log(`   - Retry after: ${retryTime.toLocaleString()}`);
      }
    }
  }
}

testSingletonConnection().catch(console.error);
