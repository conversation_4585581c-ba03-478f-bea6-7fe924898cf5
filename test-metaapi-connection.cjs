// Simple MetaAPI connection test
// Run this with: node test-metaapi-connection.js

const MetaApi = require('metaapi.cloud-sdk').default;
const fs = require('fs');

// Load environment variables manually
const envContent = fs.readFileSync('.env', 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, value] = line.split('=');
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});

async function testMetaApiConnection() {
  console.log('🔄 Testing MetaAPI connection...');
  
  const token = envVars.VITE_METAAPI_TOKEN;
  const accountId = envVars.VITE_MASTER_ACCOUNT_ID;
  
  console.log('📋 Configuration:');
  console.log(`Token: ${token ? (token.length > 10 ? token.substring(0, 10) + '...' : 'Invalid') : 'Not set'}`);
  console.log(`Account ID: ${accountId || 'Not set'}`);
  
  if (!token || token === 'your_actual_metaapi_token_here') {
    console.error('❌ MetaAPI token not configured properly');
    console.log('Please update VITE_METAAPI_TOKEN in your .env file');
    return;
  }
  
  if (!accountId || accountId === 'YOUR_ACTUAL_METAAPI_ACCOUNT_ID') {
    console.error('❌ Account ID not configured properly');
    console.log('Please update VITE_MASTER_ACCOUNT_ID in your .env file');
    return;
  }
  
  try {
    // Initialize MetaAPI
    const api = new MetaApi(token);
    console.log('✅ MetaAPI initialized');
    
    // Get accounts
    console.log('🔍 Fetching accounts...');
    const accounts = await api.metatraderAccountApi.getAccountsWithInfiniteScrollPagination();
    console.log(`📊 Found ${accounts.length} accounts`);
    
    // Find target account
    const account = accounts.find(acc => acc.login === accountId);
    if (!account) {
      console.error(`❌ Account with login ${accountId} not found`);
      console.log('Available accounts:');
      accounts.forEach(acc => {
        console.log(`  - Login: ${acc.login}, Name: ${acc.name}, State: ${acc.state}`);
      });
      return;
    }
    
    console.log(`✅ Account found: ${account.name} (${account.login})`);
    console.log(`📊 Account state: ${account.state}`);
    console.log(`🌐 Server: ${account.server}`);
    console.log(`💰 Platform: ${account.platform}`);
    
    if (account.state !== 'DEPLOYED') {
      console.warn('⚠️  Account is not deployed. Please deploy it in MetaAPI dashboard.');
      return;
    }
    
    // Test connection
    console.log('🔗 Testing streaming connection...');
    const connection = account.getStreamingConnection();
    await connection.connect();
    
    console.log('⏳ Waiting for synchronization...');
    await connection.waitSynchronized();
    
    console.log('🎉 Connection successful!');
    
    // Test getting account info
    const accountInfo = await connection.getAccountInformation();
    console.log(`💰 Balance: ${accountInfo.balance} ${accountInfo.currency}`);
    console.log(`📈 Equity: ${accountInfo.equity} ${accountInfo.currency}`);
    
    await connection.close();
    console.log('✅ Test completed successfully');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    
    if (error.message.includes('not found')) {
      console.log('💡 Make sure your account is added to MetaAPI and the login number is correct');
    } else if (error.message.includes('not deployed')) {
      console.log('💡 Deploy your account in the MetaAPI dashboard');
    } else if (error.message.includes('not connected')) {
      console.log('💡 Check your account credentials and server settings');
    } else if (error.message.includes('Unauthorized')) {
      console.log('💡 Check your MetaAPI token - it might be invalid or expired');
    }
  }
}

testMetaApiConnection().catch(console.error);
