import React, { useState } from 'react';
import Sidebar from './components/Sidebar';
import AccountOverview from './components/AccountOverview';
import LivePortfolio from './components/LivePortfolio';
import BotControls from './components/BotControls';
import PerformanceAnalytics from './components/PerformanceAnalytics';
import AIInsights from './components/AIInsights';
import MarketDataWidget from './components/MarketDataWidget';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <div className="space-y-6">
            <AccountOverview />
            <MarketDataWidget />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <LivePortfolio />
              <BotControls />
            </div>
          </div>
        );
      case 'portfolio':
        return <LivePortfolio />;
      case 'bot':
        return <BotControls />;
      case 'performance':
        return <PerformanceAnalytics />;
      case 'insights':
        return <AIInsights />;
      case 'alerts':
        return (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Notifications & Alerts</h2>
            <p className="text-gray-600 dark:text-gray-400">Alert management panel coming soon...</p>
          </div>
        );
      case 'account':
        return (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Account Management</h2>
            <p className="text-gray-600 dark:text-gray-400">Account settings panel coming soon...</p>
          </div>
        );
      case 'settings':
        return (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Settings</h2>
            <p className="text-gray-600 dark:text-gray-400">Platform settings coming soon...</p>
          </div>
        );
      default:
        return <AccountOverview />;
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar activeTab={activeTab} setActiveTab={setActiveTab} />
      <main className="flex-1 p-6 overflow-y-auto">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white capitalize">
              {activeTab === 'dashboard' ? 'Live Trading Dashboard' : activeTab.replace('-', ' ')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {activeTab === 'dashboard' && 'Real-time trading data powered by MetaAPI'}
              {activeTab === 'portfolio' && 'Monitor your live positions and trading history.'}
              {activeTab === 'bot' && 'Manage your automated trading strategies.'}
              {activeTab === 'performance' && 'Analyze your trading performance and metrics.'}
              {activeTab === 'insights' && 'AI-powered market analysis and predictions.'}
              {activeTab === 'alerts' && 'Manage your trading notifications and alerts.'}
              {activeTab === 'account' && 'Manage your account settings and preferences.'}
              {activeTab === 'settings' && 'Configure your platform settings.'}
            </p>
          </div>
          {renderContent()}
        </div>
      </main>
    </div>
  );
}

export default App;