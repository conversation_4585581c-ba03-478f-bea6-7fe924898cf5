import React, { useState, useEffect } from 'react';
import { Activity, AlertCircle, CheckCircle, Clock, Target, TrendingUp } from 'lucide-react';
import { useTradingBotContext } from '../contexts/TradingBotContext';

const BotDebugger: React.FC = () => {
  const { bot, botStatus, isRunning, isConnected } = useTradingBotContext();
  const [logs, setLogs] = useState<string[]>([]);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Capture console logs for debugging
  useEffect(() => {
    const originalLog = console.log;
    const originalError = console.error;

    console.log = (...args) => {
      const message = args.join(' ');
      if (message.includes('Trading Cycle') || message.includes('Signal') || message.includes('TRADE') || message.includes('Bot')) {
        setLogs(prev => [...prev.slice(-19), `${new Date().toLocaleTimeString()}: ${message}`]);
        setLastUpdate(new Date());
      }
      originalLog(...args);
    };

    console.error = (...args) => {
      const message = args.join(' ');
      if (message.includes('Trading') || message.includes('Bot') || message.includes('Signal')) {
        setLogs(prev => [...prev.slice(-19), `${new Date().toLocaleTimeString()}: ERROR: ${message}`]);
        setLastUpdate(new Date());
      }
      originalError(...args);
    };

    return () => {
      console.log = originalLog;
      console.error = originalError;
    };
  }, []);

  // Force a trading cycle for testing
  const forceTradingCycle = async () => {
    if (bot) {
      console.log('🔄 MANUAL: Forcing trading cycle for debugging...');
      try {
        // Use the new public method
        if ((bot as any).manualTradingCycle) {
          await (bot as any).manualTradingCycle();
        } else {
          console.log('❌ Manual trading cycle method not available');
        }
      } catch (error) {
        console.error('❌ Manual trading cycle failed:', error);
      }
    } else {
      console.log('❌ Bot not initialized - cannot force trading cycle');
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
          <Activity className="w-5 h-5" />
          <span>Bot Debugger</span>
        </h2>
        <div className="flex items-center space-x-4">
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-lg ${
            isRunning ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            <div className={`w-2 h-2 rounded-full ${isRunning ? 'bg-green-500' : 'bg-red-500'} animate-pulse`}></div>
            <span className="text-sm font-medium">{isRunning ? 'ACTIVE' : 'STOPPED'}</span>
          </div>
          <button
            onClick={forceTradingCycle}
            disabled={!bot}
            className="px-3 py-1 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 disabled:opacity-50"
          >
            Force Cycle
          </button>
        </div>
      </div>

      {/* Bot Status Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-4 h-4 text-blue-600" />
            <span className="text-sm text-blue-600">Connected</span>
          </div>
          <p className="text-lg font-bold text-blue-700 mt-1">
            {isConnected ? 'YES' : 'NO'}
          </p>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <Target className="w-4 h-4 text-green-600" />
            <span className="text-sm text-green-600">Trades Today</span>
          </div>
          <p className="text-lg font-bold text-green-700 mt-1">
            {botStatus.tradesExecutedToday}
          </p>
        </div>

        <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <TrendingUp className="w-4 h-4 text-purple-600" />
            <span className="text-sm text-purple-600">Open Positions</span>
          </div>
          <p className="text-lg font-bold text-purple-700 mt-1">
            {botStatus.openPositions}
          </p>
        </div>

        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-orange-600" />
            <span className="text-sm text-orange-600">Last Signal</span>
          </div>
          <p className="text-sm font-bold text-orange-700 mt-1">
            {botStatus.lastSignalTime ? new Date(botStatus.lastSignalTime).toLocaleTimeString() : 'Never'}
          </p>
        </div>
      </div>

      {/* Live Logs */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Live Bot Logs</h3>
          <span className="text-xs text-gray-500">Updated: {lastUpdate.toLocaleTimeString()}</span>
        </div>
        <div className="space-y-1 max-h-64 overflow-y-auto">
          {logs.length === 0 ? (
            <p className="text-sm text-gray-500 italic">No bot activity logged yet...</p>
          ) : (
            logs.map((log, index) => (
              <div key={index} className={`text-xs font-mono p-2 rounded ${
                log.includes('ERROR') ? 'bg-red-100 text-red-800' :
                log.includes('TRADE') || log.includes('🚀') ? 'bg-green-100 text-green-800' :
                log.includes('❌') ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-700'
              }`}>
                {log}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <p className="text-sm text-blue-700 dark:text-blue-300">
          <strong>Debug Info:</strong> Bot runs trading cycles every 60 seconds. 
          Confidence threshold: {isRunning ? '65%' : 'N/A'} (balanced risk). 
          Watch the logs above for real-time trading activity.
        </p>
      </div>
    </div>
  );
};

export default BotDebugger;
