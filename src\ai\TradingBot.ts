import { metaApiService, TradeSignal, MarketData, AccountInfo } from '../services/MetaApiService';
import { EURUSDStrategy, StrategyResult } from './EURUSDStrategy';
import { GoldStrategy } from './GoldStrategy';
import { CandleData } from './TechnicalIndicators';

export interface BotConfig {
  enabled: boolean;
  symbols: string[];
  riskLevel: 'conservative' | 'balanced' | 'aggressive';
  maxTradesPerDay: number;
  maxOpenPositions: number;
  accountId: string;
}

export interface BotStatus {
  isRunning: boolean;
  lastSignalTime: Date | null;
  tradesExecutedToday: number;
  openPositions: number;
  totalProfit: number;
  winRate: number;
}

export interface BotPerformance {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  totalProfit: number;
  totalLoss: number;
  winRate: number;
  profitFactor: number;
  maxDrawdown: number;
  averageWin: number;
  averageLoss: number;
}

export class TradingBot {
  private metaApi = metaApiService; // Use singleton instance
  private eurUsdStrategy: EURUSDStrategy;
  private goldStrategy: GoldStrategy;
  private config: BotConfig;
  private status: BotStatus;
  private performance: BotPerformance;
  private isInitialized: boolean = false;
  private tradingInterval: NodeJS.Timeout | null = null;
  private tradeHistory: any[] = [];

  constructor(config: BotConfig) {
    this.eurUsdStrategy = new EURUSDStrategy();
    this.goldStrategy = new GoldStrategy();
    this.config = config;
    
    this.status = {
      isRunning: false,
      lastSignalTime: null,
      tradesExecutedToday: 0,
      openPositions: 0,
      totalProfit: 0,
      winRate: 0
    };

    this.performance = {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      totalProfit: 0,
      totalLoss: 0,
      winRate: 0,
      profitFactor: 0,
      maxDrawdown: 0,
      averageWin: 0,
      averageLoss: 0
    };
  }

  async initialize(): Promise<void> {
    try {
      console.log('Initializing Trading Bot...');
      await this.metaApi.connect(this.config.accountId);
      this.isInitialized = true;
      console.log('Trading Bot initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Trading Bot:', error);
      throw error;
    }
  }

  async start(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Bot must be initialized before starting');
    }

    if (this.status.isRunning) {
      console.log('Bot is already running');
      return;
    }

    console.log('Starting Trading Bot...');
    this.status.isRunning = true;

    // Start the main trading loop
    this.tradingInterval = setInterval(async () => {
      await this.executeTradingCycle();
    }, 60000); // Run every minute

    console.log('Trading Bot started successfully');
  }

  async stop(): Promise<void> {
    console.log('Stopping Trading Bot...');
    this.status.isRunning = false;

    if (this.tradingInterval) {
      clearInterval(this.tradingInterval);
      this.tradingInterval = null;
    }

    console.log('Trading Bot stopped');
  }

  private async executeTradingCycle(): Promise<void> {
    try {
      if (!this.config.enabled || !this.status.isRunning) {
        return;
      }

      // Check trading limits
      if (this.status.tradesExecutedToday >= this.config.maxTradesPerDay) {
        console.log('Daily trade limit reached');
        return;
      }

      if (this.status.openPositions >= this.config.maxOpenPositions) {
        console.log('Maximum open positions reached');
        return;
      }

      // Update account status
      await this.updateAccountStatus();

      // Analyze each configured symbol
      for (const symbol of this.config.symbols) {
        await this.analyzeSymbol(symbol);
      }

    } catch (error) {
      console.error('Error in trading cycle:', error);
    }
  }

  private async analyzeSymbol(symbol: string): Promise<void> {
    try {
      console.log(`Analyzing ${symbol}...`);

      // Get historical data for multiple timeframes
      const m1Data = await this.getHistoricalCandles(symbol, 'M1', 100);
      const m5Data = await this.getHistoricalCandles(symbol, 'M5', 100);
      const h1Data = await this.getHistoricalCandles(symbol, 'H1', 100);
      const h4Data = await this.getHistoricalCandles(symbol, 'H4', 100);

      let strategyResult: StrategyResult;

      // Choose strategy based on symbol
      if (symbol === 'EURUSD') {
        strategyResult = await this.eurUsdStrategy.analyze(m1Data, m5Data, h1Data, h4Data);
      } else if (symbol === 'XAUUSD') {
        strategyResult = await this.goldStrategy.analyze(m1Data, m5Data, h1Data, h4Data);
      } else {
        console.log(`No strategy available for ${symbol}`);
        return;
      }

      // Execute trade if signal is valid
      if (strategyResult.signal && strategyResult.confidence >= this.getMinConfidence()) {
        await this.executeTradeSignal(strategyResult.signal, strategyResult);
      }

      console.log(`${symbol} Analysis: ${strategyResult.analysis}`);

    } catch (error) {
      console.error(`Error analyzing ${symbol}:`, error);
    }
  }

  private async executeTradeSignal(signal: TradeSignal, analysis: StrategyResult): Promise<void> {
    try {
      console.log(`Executing trade signal: ${signal.action} ${signal.symbol} at ${signal.confidence}% confidence`);

      // Apply risk management
      const adjustedSignal = this.applyRiskManagement(signal);

      // Execute the trade
      const tradeResult = await this.metaApi.executeTrade(adjustedSignal);

      // Record the trade
      const tradeRecord = {
        id: tradeResult.positionId || Date.now().toString(),
        signal: adjustedSignal,
        analysis,
        executionTime: new Date(),
        result: tradeResult,
        status: 'OPEN'
      };

      this.tradeHistory.push(tradeRecord);
      this.status.tradesExecutedToday++;
      this.status.lastSignalTime = new Date();

      console.log(`Trade executed successfully: ${tradeRecord.id}`);

    } catch (error) {
      console.error('Failed to execute trade signal:', error);
    }
  }

  private applyRiskManagement(signal: TradeSignal): TradeSignal {
    // Adjust position size based on risk level
    let riskMultiplier = 1;
    
    switch (this.config.riskLevel) {
      case 'conservative':
        riskMultiplier = 0.5;
        break;
      case 'balanced':
        riskMultiplier = 1.0;
        break;
      case 'aggressive':
        riskMultiplier = 1.5;
        break;
    }

    // Apply risk multiplier
    const adjustedVolume = signal.volume * riskMultiplier;

    // Ensure minimum and maximum limits
    const finalVolume = Math.max(0.01, Math.min(adjustedVolume, 1.0));

    return {
      ...signal,
      volume: Math.round(finalVolume * 100) / 100
    };
  }

  private async getHistoricalCandles(symbol: string, timeframe: string, count: number): Promise<CandleData[]> {
    try {
      const candles = await this.metaApi.getHistoricalData(symbol, timeframe, count);
      return candles.map(candle => ({
        open: candle.open,
        high: candle.high,
        low: candle.low,
        close: candle.close,
        volume: candle.volume || 1,
        time: candle.time
      }));
    } catch (error) {
      console.error(`Failed to get historical data for ${symbol}:`, error);
      return [];
    }
  }

  private async updateAccountStatus(): Promise<void> {
    try {
      const accountInfo = await this.metaApi.getAccountInfo();
      const openPositions = await this.metaApi.getOpenPositions();

      this.status.openPositions = openPositions.length;
      this.status.totalProfit = openPositions.reduce((total, pos) => total + pos.profit, 0);

      // Update performance metrics
      this.updatePerformanceMetrics();

    } catch (error) {
      console.error('Failed to update account status:', error);
    }
  }

  private updatePerformanceMetrics(): void {
    const closedTrades = this.tradeHistory.filter(trade => trade.status === 'CLOSED');
    
    if (closedTrades.length === 0) return;

    this.performance.totalTrades = closedTrades.length;
    this.performance.winningTrades = closedTrades.filter(trade => trade.profit > 0).length;
    this.performance.losingTrades = closedTrades.filter(trade => trade.profit <= 0).length;
    
    this.performance.totalProfit = closedTrades
      .filter(trade => trade.profit > 0)
      .reduce((total, trade) => total + trade.profit, 0);
    
    this.performance.totalLoss = Math.abs(closedTrades
      .filter(trade => trade.profit <= 0)
      .reduce((total, trade) => total + trade.profit, 0));

    this.performance.winRate = (this.performance.winningTrades / this.performance.totalTrades) * 100;
    this.performance.profitFactor = this.performance.totalLoss > 0 ? 
      this.performance.totalProfit / this.performance.totalLoss : 0;

    this.performance.averageWin = this.performance.winningTrades > 0 ? 
      this.performance.totalProfit / this.performance.winningTrades : 0;
    
    this.performance.averageLoss = this.performance.losingTrades > 0 ? 
      this.performance.totalLoss / this.performance.losingTrades : 0;

    this.status.winRate = this.performance.winRate;
  }

  private getMinConfidence(): number {
    switch (this.config.riskLevel) {
      case 'conservative':
        return 85;
      case 'balanced':
        return 75;
      case 'aggressive':
        return 65;
      default:
        return 75;
    }
  }

  // Public methods for dashboard integration
  public getStatus(): BotStatus {
    return { ...this.status };
  }

  public getPerformance(): BotPerformance {
    return { ...this.performance };
  }

  public getConfig(): BotConfig {
    return { ...this.config };
  }

  public updateConfig(newConfig: Partial<BotConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('Bot configuration updated:', this.config);
  }

  public getTradeHistory(): any[] {
    return [...this.tradeHistory];
  }

  public async getAccountInfo(): Promise<AccountInfo> {
    return await this.metaApi.getAccountInfo();
  }

  public async getCurrentMarketData(symbol: string): Promise<MarketData> {
    return await this.metaApi.getMarketData(symbol);
  }

  public disconnect(): void {
    this.stop();
    this.metaApi.disconnect();
  }
}