import { getMetaApiService, TradeSignal, MarketData, AccountInfo } from '../services/MetaApiService';
import { EURUSDStrategy, StrategyResult } from './EURUSDStrategy';
import { GoldStrategy } from './GoldStrategy';
import { CandleData } from './TechnicalIndicators';

export interface BotConfig {
  enabled: boolean;
  symbols: string[];
  riskLevel: 'conservative' | 'balanced' | 'aggressive';
  maxTradesPerDay: number;
  maxOpenPositions: number;
  accountId: string;
}

export interface BotStatus {
  isRunning: boolean;
  lastSignalTime: Date | null;
  tradesExecutedToday: number;
  openPositions: number;
  totalProfit: number;
  winRate: number;
}

export interface BotPerformance {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  totalProfit: number;
  totalLoss: number;
  winRate: number;
  profitFactor: number;
  maxDrawdown: number;
  averageWin: number;
  averageLoss: number;
}

export class TradingBot {
  private getMetaApi = () => getMetaApiService(); // Use singleton getter
  private eurUsdStrategy: EURUSDStrategy;
  private goldStrategy: GoldStrategy;
  private config: BotConfig;
  private status: BotStatus;
  private performance: BotPerformance;
  private isInitialized: boolean = false;
  private tradingInterval: NodeJS.Timeout | null = null;
  private tradeHistory: any[] = [];

  constructor(config: BotConfig) {
    this.eurUsdStrategy = new EURUSDStrategy();
    this.goldStrategy = new GoldStrategy();
    this.config = config;
    
    this.status = {
      isRunning: false,
      lastSignalTime: null,
      tradesExecutedToday: 0,
      openPositions: 0,
      totalProfit: 0,
      winRate: 0
    };

    this.performance = {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      totalProfit: 0,
      totalLoss: 0,
      winRate: 0,
      profitFactor: 0,
      maxDrawdown: 0,
      averageWin: 0,
      averageLoss: 0
    };
  }

  async initialize(): Promise<void> {
    try {
      console.log('Initializing Trading Bot...');
      await this.getMetaApi().connect(this.config.accountId);
      this.isInitialized = true;
      console.log('Trading Bot initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Trading Bot:', error);
      throw error;
    }
  }

  async start(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Bot must be initialized before starting');
    }

    if (this.status.isRunning) {
      console.log('Bot is already running');
      return;
    }

    console.log('Starting Trading Bot...');
    this.status.isRunning = true;

    // Start the main trading loop
    this.tradingInterval = setInterval(async () => {
      await this.executeTradingCycle();
    }, 30000); // ✅ CHANGED: Run every 30 seconds for more frequent trading opportunities

    // ✅ ADDED: Execute first cycle immediately
    console.log('🚀 Executing initial trading cycle...');
    setTimeout(() => this.executeTradingCycle(), 5000); // Wait 5 seconds then start

    console.log('Trading Bot started successfully');
  }

  async stop(): Promise<void> {
    console.log('Stopping Trading Bot...');
    this.status.isRunning = false;

    if (this.tradingInterval) {
      clearInterval(this.tradingInterval);
      this.tradingInterval = null;
    }

    console.log('Trading Bot stopped');
  }

  private async executeTradingCycle(): Promise<void> {
    try {
      console.log(`🔄 Trading Cycle - Enabled: ${this.config.enabled}, Running: ${this.status.isRunning}`);
      console.log(`📊 Current Status - Trades Today: ${this.status.tradesExecutedToday}/${this.config.maxTradesPerDay}, Open Positions: ${this.status.openPositions}/${this.config.maxOpenPositions}`);

      if (!this.config.enabled || !this.status.isRunning) {
        console.log('❌ Trading cycle skipped - Bot disabled or not running');
        return;
      }

      // Check trading limits
      if (this.status.tradesExecutedToday >= this.config.maxTradesPerDay) {
        console.log(`❌ Daily trade limit reached: ${this.status.tradesExecutedToday}/${this.config.maxTradesPerDay}`);
        return;
      }

      if (this.status.openPositions >= this.config.maxOpenPositions) {
        console.log(`❌ Maximum open positions reached: ${this.status.openPositions}/${this.config.maxOpenPositions}`);
        return;
      }

      console.log('✅ Trading limits OK - Proceeding with analysis...');

      // Update account status
      await this.updateAccountStatus();

      // Analyze each configured symbol
      for (const symbol of this.config.symbols) {
        console.log(`🔍 Analyzing ${symbol}...`);
        await this.analyzeSymbol(symbol);
      }

    } catch (error) {
      console.error('Error in trading cycle:', error);
    }
  }

  private async analyzeSymbol(symbol: string): Promise<void> {
    try {
      console.log(`Analyzing ${symbol}...`);

      // Get historical data for multiple timeframes
      const m1Data = await this.getHistoricalCandles(symbol, 'M1', 100);
      const m5Data = await this.getHistoricalCandles(symbol, 'M5', 100);
      const h1Data = await this.getHistoricalCandles(symbol, 'H1', 100);
      const h4Data = await this.getHistoricalCandles(symbol, 'H4', 100);

      let strategyResult: StrategyResult;

      // ✅ Get real current market price for strategy analysis
      const currentMarketData = await this.getCurrentMarketData(symbol);
      const currentPrice = currentMarketData.currentPrice || (currentMarketData.bid + currentMarketData.ask) / 2;

      // Choose strategy based on symbol with real market price
      if (symbol === 'EURUSD') {
        strategyResult = await this.eurUsdStrategy.analyze(m1Data, m5Data, h1Data, h4Data, currentPrice);
      } else if (symbol === 'XAUUSD') {
        strategyResult = await this.goldStrategy.analyze(m1Data, m5Data, h1Data, h4Data, currentPrice);
      } else {
        console.log(`No strategy available for ${symbol}`);
        return;
      }

      // Execute trade if signal is valid
      const minConfidence = this.getMinConfidence();
      console.log(`📊 ${symbol} Signal - Confidence: ${strategyResult.confidence}%, Required: ${minConfidence}%`);

      if (strategyResult.signal && strategyResult.confidence >= minConfidence) {
        console.log(`🚀 TRADE SIGNAL APPROVED for ${symbol}!`);
        console.log(`   Signal: ${strategyResult.signal.action} ${strategyResult.signal.volume} ${strategyResult.signal.symbol}`);
        console.log(`   Entry: ${strategyResult.signal.entryPrice}, SL: ${strategyResult.signal.stopLoss}, TP: ${strategyResult.signal.takeProfit}`);
        console.log(`   Confidence: ${strategyResult.confidence}% (Required: ${minConfidence}%)`);

        try {
          await this.executeTradeSignal(strategyResult.signal, strategyResult);
          console.log(`✅ Trade execution completed for ${symbol}`);
        } catch (error) {
          console.error(`❌ Trade execution failed for ${symbol}:`, error);
        }
      } else {
        console.log(`❌ ${symbol} signal rejected - ${!strategyResult.signal ? 'No signal generated' : `Low confidence (${strategyResult.confidence}% < ${minConfidence}%)`}`);
      }

      console.log(`${symbol} Analysis: ${strategyResult.analysis}`);

    } catch (error) {
      console.error(`Error analyzing ${symbol}:`, error);
    }
  }

  private async executeTradeSignal(signal: TradeSignal, analysis: StrategyResult): Promise<void> {
    try {
      console.log(`💰 EXECUTING TRADE SIGNAL: ${signal.action} ${signal.symbol} at ${signal.confidence}% confidence`);

      // Apply risk management
      const adjustedSignal = this.applyRiskManagement(signal);
      console.log(`🛡️  Risk management applied - Volume adjusted from ${signal.volume} to ${adjustedSignal.volume}`);

      // Check MetaAPI connection before executing
      const metaApi = this.getMetaApi();
      if (!metaApi.isServiceReady()) {
        throw new Error('MetaAPI service not ready for trade execution');
      }

      console.log(`📡 Sending trade order to MetaAPI...`);

      // Execute the trade
      const tradeResult = await metaApi.executeTrade(adjustedSignal);
      console.log(`📈 MetaAPI Response:`, tradeResult);

      // Record the trade
      const tradeRecord = {
        id: tradeResult.positionId || Date.now().toString(),
        signal: adjustedSignal,
        analysis,
        executionTime: new Date(),
        result: tradeResult,
        status: 'OPEN'
      };

      this.tradeHistory.push(tradeRecord);
      this.status.tradesExecutedToday++;
      this.status.openPositions++;
      this.status.lastSignalTime = new Date();

      console.log(`✅ TRADE EXECUTED SUCCESSFULLY!`);
      console.log(`   Trade ID: ${tradeRecord.id}`);
      console.log(`   Trades Today: ${this.status.tradesExecutedToday}/${this.config.maxTradesPerDay}`);
      console.log(`   Open Positions: ${this.status.openPositions}/${this.config.maxOpenPositions}`);

    } catch (error) {
      console.error('❌ TRADE EXECUTION FAILED:', error);
      console.error('   Error details:', error.message);

      // Don't throw the error to prevent stopping the bot
      // Just log it and continue with the next cycle
    }
  }

  private applyRiskManagement(signal: TradeSignal): TradeSignal {
    // Adjust position size based on risk level
    let riskMultiplier = 1;
    
    switch (this.config.riskLevel) {
      case 'conservative':
        riskMultiplier = 0.5;
        break;
      case 'balanced':
        riskMultiplier = 1.0;
        break;
      case 'aggressive':
        riskMultiplier = 1.5;
        break;
    }

    // Apply risk multiplier
    const adjustedVolume = signal.volume * riskMultiplier;

    // Ensure minimum and maximum limits
    const finalVolume = Math.max(0.01, Math.min(adjustedVolume, 1.0));

    return {
      ...signal,
      volume: Math.round(finalVolume * 100) / 100
    };
  }

  private async getHistoricalCandles(symbol: string, timeframe: string, count: number): Promise<CandleData[]> {
    try {
      const candles = await this.getMetaApi().getHistoricalData(symbol, timeframe, count);
      return candles.map(candle => ({
        open: candle.open,
        high: candle.high,
        low: candle.low,
        close: candle.close,
        volume: candle.volume || 1,
        time: candle.time
      }));
    } catch (error) {
      console.error(`Failed to get historical data for ${symbol}:`, error);
      return [];
    }
  }

  private async updateAccountStatus(): Promise<void> {
    try {
      const accountInfo = await this.getMetaApi().getAccountInfo();
      const openPositions = await this.getMetaApi().getOpenPositions();

      this.status.openPositions = openPositions.length;
      this.status.totalProfit = openPositions.reduce((total, pos) => total + pos.profit, 0);

      // Update performance metrics
      this.updatePerformanceMetrics();

    } catch (error) {
      console.error('Failed to update account status:', error);
    }
  }

  private updatePerformanceMetrics(): void {
    const closedTrades = this.tradeHistory.filter(trade => trade.status === 'CLOSED');
    
    if (closedTrades.length === 0) return;

    this.performance.totalTrades = closedTrades.length;
    this.performance.winningTrades = closedTrades.filter(trade => trade.profit > 0).length;
    this.performance.losingTrades = closedTrades.filter(trade => trade.profit <= 0).length;
    
    this.performance.totalProfit = closedTrades
      .filter(trade => trade.profit > 0)
      .reduce((total, trade) => total + trade.profit, 0);
    
    this.performance.totalLoss = Math.abs(closedTrades
      .filter(trade => trade.profit <= 0)
      .reduce((total, trade) => total + trade.profit, 0));

    this.performance.winRate = (this.performance.winningTrades / this.performance.totalTrades) * 100;
    this.performance.profitFactor = this.performance.totalLoss > 0 ? 
      this.performance.totalProfit / this.performance.totalLoss : 0;

    this.performance.averageWin = this.performance.winningTrades > 0 ? 
      this.performance.totalProfit / this.performance.winningTrades : 0;
    
    this.performance.averageLoss = this.performance.losingTrades > 0 ? 
      this.performance.totalLoss / this.performance.losingTrades : 0;

    this.status.winRate = this.performance.winRate;
  }

  private getMinConfidence(): number {
    switch (this.config.riskLevel) {
      case 'conservative':
        return 80;
      case 'balanced':
        return 65; // ✅ LOWERED from 75% to 65% to allow more trades
      case 'aggressive':
        return 55; // ✅ LOWERED from 65% to 55% for aggressive trading
      default:
        return 65;
    }
  }

  // Public methods for dashboard integration
  public getStatus(): BotStatus {
    return { ...this.status };
  }

  public getPerformance(): BotPerformance {
    return { ...this.performance };
  }

  // ✅ ADDED: Public method to manually trigger trading cycle for debugging
  public async manualTradingCycle(): Promise<void> {
    console.log('🔄 MANUAL: Triggering trading cycle for debugging...');
    await this.executeTradingCycle();
  }

  public getConfig(): BotConfig {
    return { ...this.config };
  }

  public updateConfig(newConfig: Partial<BotConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('Bot configuration updated:', this.config);
  }

  public getTradeHistory(): any[] {
    return [...this.tradeHistory];
  }

  public async getAccountInfo(): Promise<AccountInfo> {
    return await this.getMetaApi().getAccountInfo();
  }

  public async getCurrentMarketData(symbol: string): Promise<MarketData> {
    return await this.getMetaApi().getMarketData(symbol);
  }

  // ✅ NEW: Get live market data with dynamic updates for all symbols
  public async getLiveMarketDataWithUpdates(): Promise<{ [symbol: string]: MarketData }> {
    const marketData: { [symbol: string]: MarketData } = {};

    for (const symbol of this.config.symbols) {
      try {
        const data = await this.getMetaApi().getMarketData(symbol);
        marketData[symbol] = data;
      } catch (error) {
        console.error(`Failed to get market data for ${symbol}:`, error);
        // Provide fallback data to keep the system running
        marketData[symbol] = {
          symbol,
          bid: symbol === 'EURUSD' ? 1.0845 : symbol === 'XAUUSD' ? 2042.50 : 150.25,
          ask: symbol === 'EURUSD' ? 1.0847 : symbol === 'XAUUSD' ? 2042.70 : 150.27,
          time: new Date(),
          currentPrice: symbol === 'EURUSD' ? 1.0846 : symbol === 'XAUUSD' ? 2042.60 : 150.26,
          change: (Math.random() - 0.5) * 2, // Random change for demo
          changePercent: (Math.random() - 0.5) * 0.5,
          spread: 0.0002
        };
      }
    }

    return marketData;
  }

  public disconnect(): void {
    this.stop();
    // Note: Don't disconnect singleton service as other components may be using it
  }
}