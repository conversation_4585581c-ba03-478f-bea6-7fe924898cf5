import React from 'react';
import {
  LayoutDashboard,
  TrendingUp,
  Bo<PERSON>,
  BarChart3,
  Brain,
  <PERSON><PERSON><PERSON>,
  User,
  <PERSON>,
  History
} from 'lucide-react';

interface SidebarProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeTab, setActiveTab }) => {
  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { id: 'portfolio', label: 'Portfolio', icon: TrendingUp },
    { id: 'bot', label: 'Bot Settings', icon: Bot },
    { id: 'performance', label: 'Performance', icon: BarChart3 },
    { id: 'backtest', label: 'Backtest', icon: History },
    { id: 'insights', label: 'AI Insights', icon: Brain },
    { id: 'alerts', label: 'Alerts', icon: Bell },
    { id: 'account', label: 'Account', icon: User },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  return (
    <div className="w-64 bg-gray-900 text-white h-screen flex flex-col">
      <div className="p-6 border-b border-gray-700">
        <h1 className="text-xl font-bold text-blue-400">AutoTrade Pro</h1>
        <p className="text-sm text-gray-400 mt-1">Trading Platform</p>
      </div>
      
      <nav className="flex-1 py-6">
        {navItems.map((item) => {
          const Icon = item.icon;
          return (
            <button
              key={item.id}
              onClick={() => setActiveTab(item.id)}
              className={`w-full flex items-center px-6 py-3 text-left transition-colors ${
                activeTab === item.id
                  ? 'bg-blue-600 text-white border-r-3 border-blue-400'
                  : 'text-gray-300 hover:bg-gray-800 hover:text-white'
              }`}
            >
              <Icon className="w-5 h-5 mr-3" />
              {item.label}
            </button>
          );
        })}
      </nav>
      
      <div className="p-6 border-t border-gray-700">
        <div className="bg-gray-800 rounded-lg p-4">
          <p className="text-sm text-gray-400 mb-2">Subscription</p>
          <p className="text-white font-semibold">Pro Plan</p>
          <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
            <div className="bg-blue-600 h-2 rounded-full w-3/4"></div>
          </div>
          <p className="text-xs text-gray-400 mt-1">750/1000 trades used</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;