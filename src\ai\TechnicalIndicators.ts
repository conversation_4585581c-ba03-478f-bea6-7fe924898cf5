// Advanced Technical Analysis Indicators for AI Trading

export interface CandleData {
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  time: Date;
}

export class TechnicalIndicators {
  
  // Enhanced Moving Averages with different types
  static sma(data: number[], period: number): number[] {
    const result: number[] = [];
    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push(sum / period);
    }
    return result;
  }

  static ema(data: number[], period: number): number[] {
    const result: number[] = [];
    const multiplier = 2 / (period + 1);
    result[0] = data[0];
    
    for (let i = 1; i < data.length; i++) {
      result[i] = (data[i] * multiplier) + (result[i - 1] * (1 - multiplier));
    }
    return result;
  }

  // Adaptive Moving Average (KAMA) - Superior for trending markets
  static kama(data: number[], period: number = 14): number[] {
    const result: number[] = [];
    const efficiency: number[] = [];
    
    for (let i = period; i < data.length; i++) {
      const change = Math.abs(data[i] - data[i - period]);
      const volatility = data.slice(i - period + 1, i + 1)
        .reduce((sum, val, idx, arr) => {
          if (idx === 0) return sum;
          return sum + Math.abs(val - arr[idx - 1]);
        }, 0);
      
      const er = volatility !== 0 ? change / volatility : 0;
      efficiency.push(er);
      
      const fastSC = 2 / (2 + 1);
      const slowSC = 2 / (30 + 1);
      const sc = Math.pow(er * (fastSC - slowSC) + slowSC, 2);
      
      if (result.length === 0) {
        result.push(data[i]);
      } else {
        result.push(result[result.length - 1] + sc * (data[i] - result[result.length - 1]));
      }
    }
    return result;
  }

  // Advanced RSI with divergence detection
  static rsi(data: number[], period: number = 14): { rsi: number[], divergence: boolean[] } {
    const gains: number[] = [];
    const losses: number[] = [];
    
    for (let i = 1; i < data.length; i++) {
      const change = data[i] - data[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }
    
    const avgGains = this.sma(gains, period);
    const avgLosses = this.sma(losses, period);
    
    const rsi = avgGains.map((gain, i) => {
      const rs = avgLosses[i] !== 0 ? gain / avgLosses[i] : 100;
      return 100 - (100 / (1 + rs));
    });

    // Divergence detection
    const divergence = this.detectRSIDivergence(data.slice(period), rsi);
    
    return { rsi, divergence };
  }

  // MACD with histogram and signal line
  static macd(data: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {
    const fastEMA = this.ema(data, fastPeriod);
    const slowEMA = this.ema(data, slowPeriod);
    
    const macdLine = fastEMA.slice(slowPeriod - fastPeriod).map((fast, i) => 
      fast - slowEMA[i]
    );
    
    const signalLine = this.ema(macdLine, signalPeriod);
    const histogram = macdLine.slice(signalPeriod - 1).map((macd, i) => 
      macd - signalLine[i]
    );
    
    return { macdLine, signalLine, histogram };
  }

  // Bollinger Bands with squeeze detection
  static bollingerBands(data: number[], period: number = 20, stdDev: number = 2) {
    const sma = this.sma(data, period);
    const bands = sma.map((avg, i) => {
      const slice = data.slice(i, i + period);
      const variance = slice.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / period;
      const std = Math.sqrt(variance);
      
      return {
        upper: avg + (std * stdDev),
        middle: avg,
        lower: avg - (std * stdDev),
        squeeze: std < (avg * 0.01) // Squeeze detection
      };
    });
    
    return bands;
  }

  // Stochastic Oscillator with %K and %D
  static stochastic(highs: number[], lows: number[], closes: number[], kPeriod: number = 14, dPeriod: number = 3) {
    const kPercent: number[] = [];
    
    for (let i = kPeriod - 1; i < closes.length; i++) {
      const highestHigh = Math.max(...highs.slice(i - kPeriod + 1, i + 1));
      const lowestLow = Math.min(...lows.slice(i - kPeriod + 1, i + 1));
      const k = ((closes[i] - lowestLow) / (highestHigh - lowestLow)) * 100;
      kPercent.push(k);
    }
    
    const dPercent = this.sma(kPercent, dPeriod);
    
    return { kPercent, dPercent };
  }

  // Average True Range (ATR) for volatility
  static atr(highs: number[], lows: number[], closes: number[], period: number = 14): number[] {
    const trueRanges: number[] = [];
    
    for (let i = 1; i < closes.length; i++) {
      const tr1 = highs[i] - lows[i];
      const tr2 = Math.abs(highs[i] - closes[i - 1]);
      const tr3 = Math.abs(lows[i] - closes[i - 1]);
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
    
    return this.sma(trueRanges, period);
  }

  // Commodity Channel Index (CCI)
  static cci(highs: number[], lows: number[], closes: number[], period: number = 20): number[] {
    const typicalPrices = closes.map((close, i) => (highs[i] + lows[i] + close) / 3);
    const smaTP = this.sma(typicalPrices, period);
    
    return smaTP.map((sma, i) => {
      const slice = typicalPrices.slice(i, i + period);
      const meanDeviation = slice.reduce((sum, tp) => sum + Math.abs(tp - sma), 0) / period;
      return meanDeviation !== 0 ? (typicalPrices[i + period - 1] - sma) / (0.015 * meanDeviation) : 0;
    });
  }

  // Williams %R
  static williamsR(highs: number[], lows: number[], closes: number[], period: number = 14): number[] {
    const result: number[] = [];
    
    for (let i = period - 1; i < closes.length; i++) {
      const highestHigh = Math.max(...highs.slice(i - period + 1, i + 1));
      const lowestLow = Math.min(...lows.slice(i - period + 1, i + 1));
      const wr = ((highestHigh - closes[i]) / (highestHigh - lowestLow)) * -100;
      result.push(wr);
    }
    
    return result;
  }

  // Parabolic SAR
  static parabolicSAR(highs: number[], lows: number[], closes: number[], step: number = 0.02, max: number = 0.2) {
    const sar: number[] = [];
    const ep: number[] = [];
    const af: number[] = [];
    let trend = 1; // 1 for uptrend, -1 for downtrend
    
    sar[0] = lows[0];
    ep[0] = highs[0];
    af[0] = step;
    
    for (let i = 1; i < closes.length; i++) {
      if (trend === 1) {
        sar[i] = sar[i - 1] + af[i - 1] * (ep[i - 1] - sar[i - 1]);
        
        if (highs[i] > ep[i - 1]) {
          ep[i] = highs[i];
          af[i] = Math.min(af[i - 1] + step, max);
        } else {
          ep[i] = ep[i - 1];
          af[i] = af[i - 1];
        }
        
        if (lows[i] < sar[i]) {
          trend = -1;
          sar[i] = ep[i - 1];
          ep[i] = lows[i];
          af[i] = step;
        }
      } else {
        sar[i] = sar[i - 1] + af[i - 1] * (ep[i - 1] - sar[i - 1]);
        
        if (lows[i] < ep[i - 1]) {
          ep[i] = lows[i];
          af[i] = Math.min(af[i - 1] + step, max);
        } else {
          ep[i] = ep[i - 1];
          af[i] = af[i - 1];
        }
        
        if (highs[i] > sar[i]) {
          trend = 1;
          sar[i] = ep[i - 1];
          ep[i] = highs[i];
          af[i] = step;
        }
      }
    }
    
    return sar;
  }

  // Helper method for RSI divergence detection
  private static detectRSIDivergence(prices: number[], rsi: number[]): boolean[] {
    const divergence: boolean[] = new Array(rsi.length).fill(false);
    
    for (let i = 20; i < rsi.length - 5; i++) {
      const priceHigh = Math.max(...prices.slice(i - 10, i + 1));
      const priceHighIdx = prices.slice(i - 10, i + 1).indexOf(priceHigh) + i - 10;
      
      const rsiHigh = Math.max(...rsi.slice(i - 10, i + 1));
      const rsiHighIdx = rsi.slice(i - 10, i + 1).indexOf(rsiHigh) + i - 10;
      
      // Bearish divergence: price makes higher high, RSI makes lower high
      if (priceHighIdx < rsiHighIdx && prices[priceHighIdx] < prices[i] && rsi[rsiHighIdx] > rsi[i]) {
        divergence[i] = true;
      }
    }
    
    return divergence;
  }

  // Market structure analysis
  static detectMarketStructure(highs: number[], lows: number[], closes: number[]) {
    const swingHighs: number[] = [];
    const swingLows: number[] = [];
    
    for (let i = 2; i < highs.length - 2; i++) {
      if (highs[i] > highs[i - 1] && highs[i] > highs[i - 2] && 
          highs[i] > highs[i + 1] && highs[i] > highs[i + 2]) {
        swingHighs.push(i);
      }
      
      if (lows[i] < lows[i - 1] && lows[i] < lows[i - 2] && 
          lows[i] < lows[i + 1] && lows[i] < lows[i + 2]) {
        swingLows.push(i);
      }
    }
    
    return { swingHighs, swingLows };
  }
}