import MetaApi from 'metaapi.cloud-sdk';

export interface MarketData {
  symbol: string;
  bid: number;
  ask: number;
  time: Date;
  volume?: number;
  spread?: number;
}

export interface TradeSignal {
  symbol: string;
  action: 'BUY' | 'SELL';
  confidence: number;
  entryPrice: number;
  stopLoss: number;
  takeProfit: number;
  volume: number;
  timestamp: Date;
  strategy: string;
}

export interface AccountInfo {
  balance: number;
  equity: number;
  margin: number;
  freeMargin: number;
  marginLevel: number;
  accountNumber: string;
  broker: string;
  server: string;
  connected: boolean;
  currency: string;
  leverage: number;
}

export interface Position {
  id: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  volume: number;
  openPrice: number;
  currentPrice: number;
  stopLoss?: number;
  takeProfit?: number;
  profit: number;
  swap: number;
  commission: number;
  openTime: Date;
  comment?: string;
}

export interface HistoricalCandle {
  time: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export class MetaApiService {
  private api: MetaApi | null = null;
  private account: any;
  private connection: any;
  private isConnected: boolean = false;
  private isInitialized: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private priceCache: Map<string, MarketData> = new Map();
  private initializationError: string | null = null;

  constructor() {
    try {
      const token = import.meta.env.VITE_METAAPI_TOKEN;
      
      // Validate token exists and is not a placeholder
      if (!token || token === 'YOUR_METAAPI_TOKEN_HERE' || token.trim() === '') {
        this.initializationError = 'MetaAPI token not configured. Please set VITE_METAAPI_TOKEN in your .env file with your actual MetaAPI token.';
        console.error(this.initializationError);
        return;
      }

      // Validate token format (basic check)
      if (token.length < 10) {
        this.initializationError = 'Invalid MetaAPI token format. Please check your VITE_METAAPI_TOKEN in the .env file.';
        console.error(this.initializationError);
        return;
      }

      this.api = new MetaApi(token);
      console.log('MetaAPI service initialized successfully');
    } catch (error) {
      this.initializationError = `Failed to initialize MetaAPI: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error(this.initializationError, error);
    }
  }

  // Check if the service was initialized properly
  isServiceReady(): boolean {
    return this.api !== null && this.initializationError === null;
  }

  // Get initialization error if any
  getInitializationError(): string | null {
    return this.initializationError;
  }

  async connect(accountId?: string): Promise<void> {
    // Check if service is ready before attempting connection
    if (!this.isServiceReady()) {
      throw new Error(this.initializationError || 'MetaAPI service not properly initialized');
    }

    if (!this.api) {
      throw new Error('MetaAPI instance not available');
    }

    try {
      // Use the provided account ID or fallback to environment variable
      const targetAccountId = accountId || import.meta.env.VITE_MASTER_ACCOUNT_ID;
      
      if (!targetAccountId || targetAccountId === 'YOUR_ACTUAL_METAAPI_ACCOUNT_ID') {
        throw new Error('MetaAPI account ID not configured. Please update VITE_MASTER_ACCOUNT_ID in your .env file with your actual account login number.');
      }
      
      console.log('Connecting to MetaAPI account login:', targetAccountId);
      
      // Get all accounts with pagination (following your Python example)
      const accounts = await this.api.metatraderAccountApi.getAccountsWithInfiniteScrollPagination();
      
      // Find account by login (following your Python logic)
      let foundAccount = null;
      for (const account of accounts) {
        if (account.login === targetAccountId) {
          foundAccount = account;
          break;
        }
      }
      
      if (!foundAccount) {
        throw new Error(`Account with login ${targetAccountId} not found. Please ensure the account is properly configured in MetaAPI and the login number is correct.`);
      }
      
      this.account = foundAccount;
      console.log('Account found:', this.account.name);
      console.log('Account state:', this.account.state);
      console.log('Connection state:', this.account.connectionStatus);
      
      // Wait for account to be deployed and connected
      if (this.account.state !== 'DEPLOYED') {
        console.log('Waiting for account deployment...');
        await this.account.waitDeployed();
      }
      
      if (this.account.connectionStatus !== 'CONNECTED') {
        console.log('Waiting for account connection...');
        await this.account.waitConnected();
      }
      
      // Get streaming connection (following your Python example)
      this.connection = this.account.getStreamingConnection();
      await this.connection.connect();
      
      // Wait a moment for connection to stabilize (like your Python code)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Wait for terminal state synchronization
      console.log('Waiting for terminal state synchronization...');
      await this.connection.waitSynchronized();
      
      // Subscribe to market data events
      this.setupMarketDataListeners();
      
      this.isConnected = true;
      this.isInitialized = true;
      this.reconnectAttempts = 0;
      
      console.log('✅ MetaAPI connected successfully');
      
      // Subscribe to symbols we need
      await this.subscribeToSymbols(['EURUSD', 'XAUUSD', 'GBPUSD', 'USDJPY', 'GBPJPY']);
      
      // Get initial account info to verify connection
      const accountInfo = await this.getAccountInfo();
      console.log('Account verified - Balance:', accountInfo.balance, accountInfo.currency);
      
    } catch (error) {
      console.error('Failed to connect to MetaAPI:', error);
      
      // Provide more specific error messages
      if (error.message?.includes('not found')) {
        throw new Error(`Account with login ${targetAccountId} not found. Please ensure your account is properly added to MetaAPI and the login number is correct.`);
      } else if (error.message?.includes('not deployed')) {
        throw new Error('Account not deployed. Please deploy your account in MetaAPI dashboard.');
      } else if (error.message?.includes('not connected')) {
        throw new Error('Account not connected to broker. Please check your account credentials and server settings.');
      }
      
      await this.handleConnectionError(error);
      throw error;
    }
  }

  private setupMarketDataListeners(): void {
    if (!this.connection) return;

    // Listen for price updates
    this.connection.addSynchronizationListener({
      onSymbolPriceUpdated: (instanceIndex: number, price: any) => {
        const marketData: MarketData = {
          symbol: price.symbol,
          bid: price.bid,
          ask: price.ask,
          time: new Date(price.time),
          volume: price.volume,
          spread: price.ask - price.bid
        };
        
        this.priceCache.set(price.symbol, marketData);
        console.log(`Price update for ${price.symbol}: ${price.bid}/${price.ask}`);
      },
      
      onAccountInformationUpdated: (instanceIndex: number, accountInformation: any) => {
        console.log('Account information updated:', accountInformation.balance);
      },
      
      onPositionUpdated: (instanceIndex: number, position: any) => {
        console.log('Position updated:', position.symbol, position.profit);
      },
      
      onOrderUpdated: (instanceIndex: number, order: any) => {
        console.log('Order updated:', order.symbol, order.state);
      },

      onConnected: (instanceIndex: number, replicas: number) => {
        console.log('Streaming connection established');
      },

      onDisconnected: (instanceIndex: number) => {
        console.log('Streaming connection lost');
        this.isConnected = false;
      },

      onSynchronizationStarted: (instanceIndex: number) => {
        console.log('Synchronization started');
      }
    });
  }

  private async subscribeToSymbols(symbols: string[]): Promise<void> {
    if (!this.connection) return;

    try {
      for (const symbol of symbols) {
        await this.connection.subscribeToMarketData(symbol);
        console.log(`Subscribed to ${symbol} market data`);
        
        // Small delay to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } catch (error) {
      console.error('Failed to subscribe to market data:', error);
    }
  }

  private async handleConnectionError(error: any): Promise<void> {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      await new Promise(resolve => setTimeout(resolve, 5000 * this.reconnectAttempts));
      
      try {
        await this.connect();
      } catch (reconnectError) {
        console.error('Reconnection failed:', reconnectError);
      }
    } else {
      console.error('Max reconnection attempts reached');
      this.isConnected = false;
    }
  }

  async getAccountInfo(): Promise<AccountInfo> {
    if (!this.isServiceReady()) {
      throw new Error(this.initializationError || 'MetaAPI service not ready');
    }

    if (!this.connection) throw new Error('Not connected to MetaAPI');
    
    try {
      // Get account information from terminal state (following your Python example)
      const terminalState = this.connection.terminalState;
      
      if (!terminalState || !terminalState.accountInformation) {
        throw new Error('Terminal state not available. Please wait for synchronization.');
      }
      
      const accountInfo = terminalState.accountInformation;
      
      return {
        balance: accountInfo.balance || 0,
        equity: accountInfo.equity || 0,
        margin: accountInfo.margin || 0,
        freeMargin: accountInfo.marginFree || 0,
        marginLevel: accountInfo.marginLevel || 0,
        accountNumber: this.account.login || '',
        broker: this.account.brokerName || 'Unknown',
        server: this.account.server || 'Unknown',
        connected: this.isConnected,
        currency: accountInfo.currency || 'USD',
        leverage: accountInfo.leverage || 100
      };
    } catch (error) {
      console.error('Failed to get account info:', error);
      throw error;
    }
  }

  async getMarketData(symbol: string): Promise<MarketData> {
    if (!this.isServiceReady()) {
      throw new Error(this.initializationError || 'MetaAPI service not ready');
    }

    // First try to get from cache (real-time data)
    const cachedData = this.priceCache.get(symbol);
    if (cachedData && (Date.now() - cachedData.time.getTime()) < 10000) { // 10 seconds fresh
      return cachedData;
    }

    // Fallback to direct API call
    if (!this.connection) throw new Error('Not connected to MetaAPI');
    
    try {
      const price = await this.connection.getSymbolPrice(symbol);
      const marketData: MarketData = {
        symbol,
        bid: price.bid,
        ask: price.ask,
        time: new Date(price.time),
        volume: price.volume,
        spread: price.ask - price.bid
      };
      
      this.priceCache.set(symbol, marketData);
      return marketData;
    } catch (error) {
      console.error(`Failed to get market data for ${symbol}:`, error);
      throw error;
    }
  }

  async getHistoricalData(symbol: string, timeframe: string, count: number): Promise<HistoricalCandle[]> {
    if (!this.isServiceReady()) {
      throw new Error(this.initializationError || 'MetaAPI service not ready');
    }

    if (!this.connection) throw new Error('Not connected to MetaAPI');
    
    try {
      console.log(`Fetching ${count} ${timeframe} candles for ${symbol}`);
      
      const candles = await this.connection.getCandles(symbol, timeframe, count);
      
      return candles.map((candle: any) => ({
        time: new Date(candle.time),
        open: candle.open,
        high: candle.high,
        low: candle.low,
        close: candle.close,
        volume: candle.tickVolume || candle.realVolume || 1
      }));
    } catch (error) {
      console.error(`Failed to get historical data for ${symbol}:`, error);
      throw error;
    }
  }

  async executeTrade(signal: TradeSignal): Promise<any> {
    if (!this.isServiceReady()) {
      throw new Error(this.initializationError || 'MetaAPI service not ready');
    }

    if (!this.connection) throw new Error('Not connected to MetaAPI');
    
    try {
      console.log(`Executing trade: ${signal.action} ${signal.volume} ${signal.symbol}`);
      
      // Use the same approach as your Python code
      let result;
      if (signal.action === 'BUY') {
        result = await this.connection.createMarketBuyOrder(
          signal.symbol,
          signal.volume,
          signal.stopLoss,
          signal.takeProfit,
          {
            comment: `AI Bot - ${signal.strategy} (${signal.confidence}%)`
          }
        );
      } else {
        result = await this.connection.createMarketSellOrder(
          signal.symbol,
          signal.volume,
          signal.stopLoss,
          signal.takeProfit,
          {
            comment: `AI Bot - ${signal.strategy} (${signal.confidence}%)`
          }
        );
      }

      console.log('Trade executed successfully:', result);
      return result;
    } catch (error) {
      console.error('Failed to execute trade:', error);
      throw error;
    }
  }

  async getOpenPositions(): Promise<Position[]> {
    if (!this.isServiceReady()) {
      throw new Error(this.initializationError || 'MetaAPI service not ready');
    }

    if (!this.connection) throw new Error('Not connected to MetaAPI');
    
    try {
      // Get positions from terminal state (following your Python example)
      const terminalState = this.connection.terminalState;
      
      if (!terminalState || !terminalState.positions) {
        return [];
      }
      
      const positions = terminalState.positions;
      
      return positions.map((position: any) => ({
        id: position.id,
        symbol: position.symbol,
        type: position.type === 'POSITION_TYPE_BUY' ? 'BUY' : 'SELL',
        volume: position.volume,
        openPrice: position.openPrice,
        currentPrice: position.currentPrice,
        stopLoss: position.stopLoss,
        takeProfit: position.takeProfit,
        profit: position.profit,
        swap: position.swap || 0,
        commission: position.commission || 0,
        openTime: new Date(position.time),
        comment: position.comment
      }));
    } catch (error) {
      console.error('Failed to get open positions:', error);
      throw error;
    }
  }

  async closePosition(positionId: string): Promise<any> {
    if (!this.isServiceReady()) {
      throw new Error(this.initializationError || 'MetaAPI service not ready');
    }

    if (!this.connection) throw new Error('Not connected to MetaAPI');
    
    try {
      const result = await this.connection.closePosition(positionId);
      console.log('Position closed:', result);
      return result;
    } catch (error) {
      console.error('Failed to close position:', error);
      throw error;
    }
  }

  async getSymbolSpecification(symbol: string): Promise<any> {
    if (!this.isServiceReady()) {
      throw new Error(this.initializationError || 'MetaAPI service not ready');
    }

    if (!this.connection) throw new Error('Not connected to MetaAPI');
    
    try {
      const spec = await this.connection.getSymbolSpecification(symbol);
      return spec;
    } catch (error) {
      console.error(`Failed to get symbol specification for ${symbol}:`, error);
      throw error;
    }
  }

  // Get real-time market data for multiple symbols
  async getMultipleMarketData(symbols: string[]): Promise<{ [symbol: string]: MarketData }> {
    if (!this.isServiceReady()) {
      throw new Error(this.initializationError || 'MetaAPI service not ready');
    }

    const marketData: { [symbol: string]: MarketData } = {};
    
    try {
      const promises = symbols.map(async (symbol) => {
        try {
          const data = await this.getMarketData(symbol);
          marketData[symbol] = data;
        } catch (error) {
          console.error(`Failed to get data for ${symbol}:`, error);
        }
      });
      
      await Promise.all(promises);
      return marketData;
    } catch (error) {
      console.error('Failed to get multiple market data:', error);
      return marketData;
    }
  }

  // Check connection status
  isConnectionHealthy(): boolean {
    return this.isServiceReady() && this.isConnected && this.isInitialized;
  }

  // Get connection statistics
  getConnectionStats(): any {
    return {
      serviceReady: this.isServiceReady(),
      connected: this.isConnected,
      initialized: this.isInitialized,
      reconnectAttempts: this.reconnectAttempts,
      cachedSymbols: Array.from(this.priceCache.keys()),
      lastUpdate: this.priceCache.size > 0 ? 
        Math.max(...Array.from(this.priceCache.values()).map(data => data.time.getTime())) : null,
      accountId: this.account?.login || 'Unknown',
      server: this.account?.server || 'Unknown',
      initializationError: this.initializationError
    };
  }

  disconnect(): void {
    try {
      if (this.connection) {
        this.connection.close();
      }
      
      this.isConnected = false;
      this.isInitialized = false;
      this.priceCache.clear();
      
      console.log('MetaAPI disconnected');
    } catch (error) {
      console.error('Error during disconnect:', error);
    }
  }
}