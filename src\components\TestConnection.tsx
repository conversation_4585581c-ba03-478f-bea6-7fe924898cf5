import React, { useState, useEffect } from 'react';
import { getMetaApiService } from '../services/MetaApiService';

const TestConnection: React.FC = () => {
  const [status, setStatus] = useState<string>('Testing...');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testConnection = async () => {
      try {
        const metaApiService = getMetaApiService();
        
        if (!metaApiService.isServiceReady()) {
          const initError = metaApiService.getInitializationError();
          throw new Error(initError || 'MetaAPI service not ready');
        }
        
        setStatus('✅ MetaAPI Service Ready');
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setStatus('❌ MetaAPI Service Failed');
      }
    };

    testConnection();
  }, []);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
        Connection Test
      </h2>
      <div className="space-y-2">
        <p className="text-gray-600 dark:text-gray-400">Status: {status}</p>
        {error && (
          <p className="text-red-600 dark:text-red-400">Error: {error}</p>
        )}
      </div>
    </div>
  );
};

export default TestConnection;
