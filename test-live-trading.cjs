const { TradingBot } = require('./dist/ai/TradingBot.js');

async function testLiveTrading() {
  console.log('🚀 Testing Live Trading Bot...\n');

  const config = {
    enabled: true,
    symbols: ['EURUSD', 'XAUUSD'],
    riskLevel: 'balanced',
    maxTradesPerDay: 10,
    maxOpenPositions: 3,
    accountId: process.env.VITE_MASTER_ACCOUNT_ID || '168683'
  };

  console.log('📋 Bot Configuration:');
  console.log(`   Enabled: ${config.enabled}`);
  console.log(`   Symbols: ${config.symbols.join(', ')}`);
  console.log(`   Risk Level: ${config.riskLevel}`);
  console.log(`   Max Trades/Day: ${config.maxTradesPerDay}`);
  console.log(`   Max Open Positions: ${config.maxOpenPositions}`);
  console.log(`   Account ID: ${config.accountId}\n`);

  // Initialize bot
  console.log('🔧 Initializing Trading Bot...');
  const bot = new TradingBot(config);
  
  try {
    await bot.initialize();
    console.log('✅ Bot initialized successfully\n');
  } catch (error) {
    console.log(`❌ Bot initialization failed: ${error.message}`);
    console.log('🔄 Continuing with test...\n');
  }

  // Check minimum confidence levels
  console.log('📊 Confidence Thresholds:');
  const minConfidence = bot.getMinConfidence ? bot.getMinConfidence() : 65;
  console.log(`   Required Confidence: ${minConfidence}%`);
  console.log(`   This means signals need ${minConfidence}%+ confidence to execute\n`);

  // Test bot status
  console.log('🤖 Bot Status:');
  const status = bot.getStatus();
  console.log(`   Running: ${status.isRunning}`);
  console.log(`   Trades Today: ${status.tradesExecutedToday}`);
  console.log(`   Open Positions: ${status.openPositions}`);
  console.log(`   Last Signal: ${status.lastSignalTime || 'Never'}\n`);

  // Start bot and run for a few cycles
  console.log('🚀 Starting bot for live testing...');
  try {
    await bot.start();
    console.log('✅ Bot started successfully');
    
    // Let it run for 3 minutes to see if it generates any trades
    console.log('⏱️  Running for 3 minutes to test live trading...');
    console.log('   Watch the console for trading signals and execution attempts\n');
    
    await new Promise(resolve => setTimeout(resolve, 180000)); // 3 minutes
    
    // Check final status
    const finalStatus = bot.getStatus();
    console.log('\n📈 Final Results:');
    console.log(`   Trades Executed: ${finalStatus.tradesExecutedToday}`);
    console.log(`   Open Positions: ${finalStatus.openPositions}`);
    console.log(`   Last Signal: ${finalStatus.lastSignalTime || 'Never'}`);
    
    await bot.stop();
    console.log('✅ Bot stopped successfully');
    
  } catch (error) {
    console.log(`❌ Bot testing failed: ${error.message}`);
  }

  console.log('\n🎯 Test Complete!');
}

// Run the test
testLiveTrading().catch(console.error);
