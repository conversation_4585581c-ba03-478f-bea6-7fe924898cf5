import React from 'react';
import { Brain, TrendingUp, TrendingDown, AlertCircle, Target, BarChart3 } from 'lucide-react';

const AIInsights: React.FC = () => {
  const marketSentiment = [
    { pair: 'EURUSD', sentiment: 'Bullish', confidence: 87, direction: 'up', change: '+0.23%' },
    { pair: 'XAUUSD', sentiment: 'Bearish', confidence: 74, direction: 'down', change: '-0.45%' },
    { pair: 'GBPJPY', sentiment: 'Neutral', confidence: 56, direction: 'neutral', change: '+0.12%' },
    { pair: 'USDJPY', sentiment: 'Bullish', confidence: 82, direction: 'up', change: '+0.31%' }
  ];

  const upcomingSignals = [
    {
      pair: 'EURUSD',
      type: 'BUY',
      confidence: 91,
      targetEntry: '1.0845',
      expectedTime: '15-30 min',
      reasonCode: 'Trend Continuation + RSI Recovery'
    },
    {
      pair: 'XAUUSD',
      type: 'SELL',
      confidence: 78,
      targetEntry: '2042.50',
      expectedTime: '1-2 hours',
      reasonCode: 'Resistance Level + Dollar Strength'
    }
  ];

  const newsImpact = [
    {
      event: 'ECB Interest Rate Decision',
      time: '14:15 GMT',
      impact: 'High',
      affected: ['EURUSD', 'EURGBP', 'EURJPY'],
      recommendation: 'Avoid EUR pairs 30min before/after'
    },
    {
      event: 'US NFP Release',
      time: 'Tomorrow 13:30 GMT',
      impact: 'Very High',
      affected: ['USDJPY', 'EURUSD', 'GBPUSD'],
      recommendation: 'High volatility expected - reduce position sizes'
    }
  ];

  return (
    <div className="space-y-6">
      {/* AI Market Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
            <Brain className="w-6 h-6 text-purple-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">AI Market Insights</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-4 text-white">
            <p className="text-blue-100 text-sm">Market Strength</p>
            <p className="text-2xl font-bold">Moderate</p>
            <p className="text-blue-200 text-sm">Trending markets detected</p>
          </div>
          
          <div className="bg-gradient-to-r from-green-500 to-teal-600 rounded-lg p-4 text-white">
            <p className="text-green-100 text-sm">Signal Quality</p>
            <p className="text-2xl font-bold">High</p>
            <p className="text-green-200 text-sm">85% avg confidence</p>
          </div>
          
          <div className="bg-gradient-to-r from-orange-500 to-red-600 rounded-lg p-4 text-white">
            <p className="text-orange-100 text-sm">Volatility</p>
            <p className="text-2xl font-bold">Medium</p>
            <p className="text-orange-200 text-sm">News events pending</p>
          </div>
          
          <div className="bg-gradient-to-r from-indigo-500 to-blue-600 rounded-lg p-4 text-white">
            <p className="text-indigo-100 text-sm">Opportunities</p>
            <p className="text-2xl font-bold">3</p>
            <p className="text-indigo-200 text-sm">High probability setups</p>
          </div>
        </div>
      </div>

      {/* Market Sentiment Analysis */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Real-time Market Sentiment</h3>
        <div className="space-y-4">
          {marketSentiment.map((item, index) => (
            <div key={index} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="font-semibold text-gray-900 dark:text-white w-20">{item.pair}</div>
                <div className="flex items-center space-x-2">
                  {item.direction === 'up' && <TrendingUp className="w-4 h-4 text-green-500" />}
                  {item.direction === 'down' && <TrendingDown className="w-4 h-4 text-red-500" />}
                  {item.direction === 'neutral' && <BarChart3 className="w-4 h-4 text-gray-500" />}
                  <span className={`font-medium ${
                    item.sentiment === 'Bullish' ? 'text-green-600' :
                    item.sentiment === 'Bearish' ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {item.sentiment}
                  </span>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Confidence</p>
                  <p className="font-semibold text-gray-900 dark:text-white">{item.confidence}%</p>
                </div>
                <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${
                      item.confidence >= 80 ? 'bg-green-500' :
                      item.confidence >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${item.confidence}%` }}
                  ></div>
                </div>
                <div className={`font-medium ${
                  item.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                }`}>
                  {item.change}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Upcoming High-Confidence Signals */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Predicted Trading Opportunities</h3>
        <div className="space-y-4">
          {upcomingSignals.map((signal, index) => (
            <div key={index} className="p-4 border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg bg-blue-50 dark:bg-blue-900/20">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                    signal.type === 'BUY' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}>
                    {signal.type} {signal.pair}
                  </div>
                  <div className="flex items-center space-x-1">
                    <Target className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-600 dark:text-blue-400">Entry: {signal.targetEntry}</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600 dark:text-gray-400">Confidence</p>
                  <p className="text-lg font-bold text-blue-600">{signal.confidence}%</p>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Expected timing: <span className="font-medium">{signal.expectedTime}</span></p>
                  <p className="text-gray-600 dark:text-gray-400">Reason: <span className="font-medium">{signal.reasonCode}</span></p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* News Impact Analysis */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">News Impact Analysis</h3>
        <div className="space-y-4">
          {newsImpact.map((news, index) => (
            <div key={index} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-3">
                  <AlertCircle className={`w-5 h-5 ${
                    news.impact === 'Very High' ? 'text-red-500' :
                    news.impact === 'High' ? 'text-orange-500' : 'text-yellow-500'
                  }`} />
                  <div>
                    <p className="font-semibold text-gray-900 dark:text-white">{news.event}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{news.time}</p>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  news.impact === 'Very High' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                  news.impact === 'High' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                  'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                }`}>
                  {news.impact} Impact
                </span>
              </div>
              <div className="space-y-2">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Affected pairs: <span className="font-medium">{news.affected.join(', ')}</span>
                  </p>
                </div>
                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg">
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    <strong>AI Recommendation:</strong> {news.recommendation}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Historical Backtest Performance */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">AI Strategy Backtesting</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">30-Day Backtest</p>
            <p className="text-2xl font-bold text-green-600">+18.7%</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Win Rate: 79.2%</p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">90-Day Backtest</p>
            <p className="text-2xl font-bold text-green-600">+42.3%</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Win Rate: 76.8%</p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">1-Year Backtest</p>
            <p className="text-2xl font-bold text-green-600">+187.5%</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">Win Rate: 74.1%</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIInsights;